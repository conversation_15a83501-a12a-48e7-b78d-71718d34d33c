import { CatalogType } from '#domain/aggregates/catalog/Catalog.Entity';
import CatalogDiscountOperator from '#domain/aggregates/catalogDiscount/CatalogDiscount.Operator';
import ClientEntity from '#domain/aggregates/client/Client.Entity';
import StoreDiscountOperator from '#domain/aggregates/storeDiscount/StoreDiscount.Operator';
import { TaxType } from '#domain/aggregates/tax/Tax.Entity';
import TaxOperator from '#domain/aggregates/tax/Tax.Operator';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';
import DomainUtil from '#domain/common/Domain.Util';

import service, {
  CatalogDiscountWithPriceAfterDiscount, CatalogWithQuantity,
  ProcessableDiscount,
  StoreDiscountWithPriceAfterDiscount,
} from './Catalog.DomainService';

jest.unmock('./Catalog.DomainService');

describe('Catalog DomainService - calculateDiscountsForCatalogByClient', () => {
  const baseCatalogItem: CatalogWithQuantity = {
    id: '1',
    price: 100,
    name: 'Item 1',
    attributes: [],
    companyId: '1',
    description: 'Description',
    disabledAt: null,
    discountIds: ['1'],
    inventoryRelations: [],
    readId: '1',
    requiresStock: false,
    taxIds: [],
    type: CatalogType.PRODUCT,
    createdAt: new Date(),
    updatedAt: new Date(),
    quantity: 1,
    mediaIds: [],
  };

  const baseCatalogDiscount: CatalogDiscountWithPriceAfterDiscount = {
    id: '1',
    catalogIds: ['1'],
    clientIds: ['client1'],
    discountType: DiscountType.PERCENTAGE,
    discountValue: 10,
    priceAfterDiscount: 90,
    requiredQuantity: 0,
    companyId: '1',
    startDate: new Date(),
    endDate: new Date(),
    disabledAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    name: '10%  catalog discount',
  };

  const processableBaseCatalogDiscount: ProcessableDiscount = {
    ...baseCatalogDiscount,
    priority: 0,
    hasRequiredValue: true,
  };

  const baseStoreDiscount: StoreDiscountWithPriceAfterDiscount = {
    id: '1',
    clientIds: ['client1'],
    discountType: DiscountType.PERCENTAGE,
    discountValue: 15,
    priceAfterDiscount: 85,
    name: '15% store discount',
    requiredAmount: 0,
    companyId: '1',
    startDate: new Date(),
    endDate: new Date(),
    disabledAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const processableBaseStoreDiscount: ProcessableDiscount = {
    ...baseStoreDiscount,
    priority: 0,
    hasRequiredValue: true,
  };

  const baseClient: ClientEntity = {
    id: 'client1',
    clientCompanyId: '1',
    name: 'Client 1',
    companyId: '1',
    storeDiscounts: [],
    tributaryId: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(() => {
    jest.clearAllMocks();

    (CatalogDiscountOperator.hasCatalogAccess as jest.Mock).mockReturnValue(true);
    (CatalogDiscountOperator.hasRequiredQuantity as jest.Mock).mockReturnValue(true);
    (CatalogDiscountOperator.isApplicable as jest.Mock).mockReturnValue(true);
    (CatalogDiscountOperator.hasClientAccess as jest.Mock).mockReturnValue(true);
    (CatalogDiscountOperator.hasGlobalAccess as jest.Mock).mockReturnValue(false);
    (CatalogDiscountOperator.calculatePriceAfterDiscount as jest.Mock).mockReturnValue(90);

    (StoreDiscountOperator.hasRequiredAmount as jest.Mock).mockReturnValue(true);
    (StoreDiscountOperator.isApplicable as jest.Mock).mockReturnValue(true);
    (StoreDiscountOperator.hasClientAccess as jest.Mock).mockReturnValue(true);
    (StoreDiscountOperator.hasGlobalAccess as jest.Mock).mockReturnValue(false);
    (StoreDiscountOperator.calculatePriceAfterDiscount as jest.Mock).mockReturnValue(85);

    (DomainUtil.toOperablePrice as jest.Mock).mockReturnValue(100);
    (DomainUtil.toDisplayPrice as jest.Mock).mockReturnValue(100);
  });

  it('applies a single applicable catalog discount', () => {
    const result = service.calculateDiscountsForCatalogByClient({
      catalog: [baseCatalogItem], catalogDiscounts: [baseCatalogDiscount], storeDiscounts: [], client: baseClient,
    });

    expect(result.length).toBe(1);
    expect(result[0]).toEqual({
      ...baseCatalogItem,
      priceAfterDiscount: 90,
      appliedDiscount: { catalogDiscount: baseCatalogDiscount, storeDiscount: null },
      applicableDiscounts: { catalogDiscounts: [baseCatalogDiscount], storeDiscounts: [] },
    });

    expect(CatalogDiscountOperator.hasCatalogAccess).toHaveBeenCalledWith(baseCatalogDiscount, baseCatalogItem.id);
    expect(CatalogDiscountOperator.isApplicable).toHaveBeenCalledWith(processableBaseCatalogDiscount, baseClient.id);
    expect(CatalogDiscountOperator.hasRequiredQuantity).toHaveBeenCalledWith(baseCatalogDiscount, baseCatalogItem.quantity);
    expect(CatalogDiscountOperator.calculatePriceAfterDiscount).toHaveBeenCalledWith(processableBaseCatalogDiscount, baseCatalogItem.price);
    expect(CatalogDiscountOperator.hasClientAccess).toHaveBeenCalledWith(processableBaseCatalogDiscount, baseClient.id);
    expect(CatalogDiscountOperator.hasGlobalAccess).toHaveBeenCalledWith(processableBaseCatalogDiscount);
  });

  it('applies store discount if no catalog discount is found', () => {
    (CatalogDiscountOperator.hasCatalogAccess as jest.Mock).mockReturnValue(false);

    const result = service.calculateDiscountsForCatalogByClient({
      catalog: [baseCatalogItem],
      catalogDiscounts: [],
      storeDiscounts: [baseStoreDiscount],
      client: baseClient,
    });

    expect(result.length).toBe(1);
    expect(result[0]).toEqual({
      ...baseCatalogItem,
      priceAfterDiscount: 85,
      appliedDiscount: { catalogDiscount: null, storeDiscount: baseStoreDiscount },
      applicableDiscounts: { catalogDiscounts: [], storeDiscounts: [baseStoreDiscount] },
    });

    expect(StoreDiscountOperator.isApplicable).toHaveBeenNthCalledWith(1, processableBaseStoreDiscount, baseClient.id);
    expect(StoreDiscountOperator.hasRequiredAmount).toHaveBeenNthCalledWith(1, baseStoreDiscount, 100);
    expect(StoreDiscountOperator.calculatePriceAfterDiscount).toHaveBeenNthCalledWith(1, processableBaseStoreDiscount, 100);
    expect(StoreDiscountOperator.hasClientAccess).toHaveBeenNthCalledWith(1, processableBaseStoreDiscount, baseClient.id);
    expect(StoreDiscountOperator.hasGlobalAccess).toHaveBeenNthCalledWith(1, processableBaseStoreDiscount);
    expect(DomainUtil.toOperablePrice).toHaveBeenNthCalledWith(1, 100);
    expect(DomainUtil.toDisplayPrice).toHaveBeenNthCalledWith(1, 100);
  });

  it('applies catalog discount even if store discount is found', () => {
    const result = service.calculateDiscountsForCatalogByClient({
      catalog: [baseCatalogItem], catalogDiscounts: [baseCatalogDiscount], storeDiscounts: [baseStoreDiscount], client: baseClient,
    });

    expect(result.length).toBe(1);
    expect(result[0]).toEqual({
      ...baseCatalogItem,
      priceAfterDiscount: 90,
      appliedDiscount: { catalogDiscount: baseCatalogDiscount, storeDiscount: null },
      applicableDiscounts: { catalogDiscounts: [baseCatalogDiscount], storeDiscounts: [] },
    });
  });

  it('Return the same price if no discount is found', () => {
    (CatalogDiscountOperator.hasCatalogAccess as jest.Mock).mockReturnValue(false);
    (StoreDiscountOperator.isApplicable as jest.Mock).mockReturnValue(false);

    const result = service.calculateDiscountsForCatalogByClient({
      catalog: [baseCatalogItem], catalogDiscounts: [], storeDiscounts: [], client: baseClient,
    });

    expect(result.length).toBe(1);
    expect(result[0]).toEqual({
      ...baseCatalogItem,
      priceAfterDiscount: 100,
      appliedDiscount: { catalogDiscount: null, storeDiscount: null },
      applicableDiscounts: { catalogDiscounts: [], storeDiscounts: [] },
    });

    expect(CatalogDiscountOperator.hasCatalogAccess).not.toHaveBeenCalled();
    expect(CatalogDiscountOperator.isApplicable).not.toHaveBeenCalled();
    expect(StoreDiscountOperator.isApplicable).not.toHaveBeenCalled();
  });

  it('Throws an error if client has more than one catalog discount for the same catalog item', () => {
    (CatalogDiscountOperator.hasClientAccess as jest.Mock).mockReturnValue(true);
    (CatalogDiscountOperator.hasGlobalAccess as jest.Mock).mockReturnValue(false);

    const catalogDiscount2 = { ...baseCatalogDiscount, id: '2' };

    expect(() => service.calculateDiscountsForCatalogByClient({
      catalog: [baseCatalogItem],
      catalogDiscounts: [baseCatalogDiscount, catalogDiscount2],
      storeDiscounts: [],
      client: baseClient,
    })).toThrow(new Error('Client has more than one applicable discount for the same catalog item and required quantity/amount', { cause: 'CONFLICT' }));
  });

  it('Throws an error if client has more than one store discount', () => {
    (CatalogDiscountOperator.hasCatalogAccess as jest.Mock).mockReturnValue(false);
    (StoreDiscountOperator.hasClientAccess as jest.Mock).mockReturnValue(true);
    (StoreDiscountOperator.hasGlobalAccess as jest.Mock).mockReturnValue(false);

    const storeDiscount2 = { ...baseStoreDiscount, id: '2' };

    expect(() => service.calculateDiscountsForCatalogByClient({
      catalog: [baseCatalogItem],
      catalogDiscounts: [],
      storeDiscounts: [baseStoreDiscount, storeDiscount2],
      client: baseClient,
    })).toThrow(new Error('Client has more than one applicable discount for the same catalog item and required quantity/amount', { cause: 'CONFLICT' }));
  });

  it('Applies multiple catalog discounts for many catalog items that matches different discounts', () => {
    const catalogItem2 = { ...baseCatalogItem, id: '2', price: 200 };
    const catalogDiscount2 = {
      ...baseCatalogDiscount, id: '2', catalogIds: ['2'], priceAfterDiscount: 180,
    };

    (CatalogDiscountOperator.hasCatalogAccess as jest.Mock)
      .mockReturnValueOnce(true)
      .mockReturnValueOnce(false)
      .mockReturnValueOnce(false)
      .mockReturnValueOnce(true);

    (CatalogDiscountOperator.calculatePriceAfterDiscount as jest.Mock)
      .mockReturnValueOnce(90)
      .mockReturnValueOnce(180);

    const result = service.calculateDiscountsForCatalogByClient({
      catalog: [baseCatalogItem, catalogItem2],
      catalogDiscounts: [baseCatalogDiscount, catalogDiscount2],
      storeDiscounts: [baseStoreDiscount],
      client: baseClient,
    });

    expect(result).toEqual([
      {
        ...baseCatalogItem,
        priceAfterDiscount: 90,
        appliedDiscount: { catalogDiscount: baseCatalogDiscount, storeDiscount: null },
        applicableDiscounts: { catalogDiscounts: [baseCatalogDiscount], storeDiscounts: [] },
      },
      {
        ...catalogItem2,
        priceAfterDiscount: 180,
        appliedDiscount: { catalogDiscount: catalogDiscount2, storeDiscount: null },
        applicableDiscounts: { catalogDiscounts: [catalogDiscount2], storeDiscounts: [] },
      },
    ]);
  });

  it('Applies multiple store discounts for many catalog items that matches different discounts', () => {
    const catalogItem2 = { ...baseCatalogItem, id: '2', price: 200 };
    const expectedStoreDiscount = {
      ...baseStoreDiscount, discountValue: 15, priceAfterDiscount: 170,
    };

    (CatalogDiscountOperator.hasCatalogAccess as jest.Mock).mockReturnValue(false);

    (StoreDiscountOperator.calculatePriceAfterDiscount as jest.Mock)
      .mockReturnValueOnce(85)
      .mockReturnValueOnce(170);

    const result = service.calculateDiscountsForCatalogByClient({
      catalog: [baseCatalogItem, catalogItem2],
      catalogDiscounts: [],
      storeDiscounts: [baseStoreDiscount],
      client: baseClient,
    });

    expect(result).toEqual([
      {
        ...baseCatalogItem,
        priceAfterDiscount: 85,
        appliedDiscount: { catalogDiscount: null, storeDiscount: baseStoreDiscount },
        applicableDiscounts: { catalogDiscounts: [], storeDiscounts: [baseStoreDiscount] },
      },
      {
        ...catalogItem2,
        priceAfterDiscount: 170,
        appliedDiscount: { catalogDiscount: null, storeDiscount: expectedStoreDiscount },
        applicableDiscounts: { catalogDiscounts: [], storeDiscounts: [expectedStoreDiscount] },
      },
    ]);
  });

  it('Applies for multiple catalog discounts but different applicable clients', () => {
    const catalogDiscount2 = {
      ...baseCatalogDiscount, id: '2', clientIds: ['client2'],
    };

    (CatalogDiscountOperator.isApplicable as jest.Mock)
      .mockReturnValueOnce(true)
      .mockReturnValueOnce(false);

    (CatalogDiscountOperator.isApplicable as jest.Mock).mockImplementation((discount, clientId) => discount.clientIds.includes(clientId));

    const result = service.calculateDiscountsForCatalogByClient({
      catalog: [baseCatalogItem],
      catalogDiscounts: [baseCatalogDiscount, catalogDiscount2],
      storeDiscounts: [baseStoreDiscount],
      client: baseClient,
    });

    expect(result).toEqual([
      {
        ...baseCatalogItem,
        priceAfterDiscount: 90,
        appliedDiscount: { catalogDiscount: baseCatalogDiscount, storeDiscount: null },
        applicableDiscounts: { catalogDiscounts: [baseCatalogDiscount], storeDiscounts: [] },
      },
    ]);
  });

  it('Applies for multiple store discounts but different applicable clients', () => {
    const storeDiscount2: StoreDiscountWithPriceAfterDiscount = {
      ...baseStoreDiscount, id: '2', clientIds: ['client2'],
    };

    (StoreDiscountOperator.hasClientAccess as jest.Mock).mockReturnValueOnce(true);
    (StoreDiscountOperator.hasClientAccess as jest.Mock).mockReturnValueOnce(false);

    const result = service.calculateDiscountsForCatalogByClient({
      catalog: [baseCatalogItem],
      catalogDiscounts: [],
      storeDiscounts: [baseStoreDiscount, storeDiscount2],
      client: baseClient,
    });

    expect(result).toEqual([
      {
        ...baseCatalogItem,
        priceAfterDiscount: 85,
        appliedDiscount: { catalogDiscount: null, storeDiscount: baseStoreDiscount },
        applicableDiscounts: { catalogDiscounts: [], storeDiscounts: [baseStoreDiscount] },
      },
    ]);
  });

  it('Should return catalog applicable discounts if not any quantity matches and there is also a store applicable discount', () => {
    (CatalogDiscountOperator.hasRequiredQuantity as jest.Mock).mockReturnValue(false);
    (CatalogDiscountOperator.hasCatalogAccess as jest.Mock).mockReturnValue(true);
    (StoreDiscountOperator.hasRequiredAmount as jest.Mock).mockReturnValue(false);

    const result = service.calculateDiscountsForCatalogByClient({
      catalog: [baseCatalogItem],
      catalogDiscounts: [baseCatalogDiscount],
      storeDiscounts: [baseStoreDiscount],
      client: baseClient,
    });

    expect(result).toEqual([
      {
        ...baseCatalogItem,
        priceAfterDiscount: 100,
        appliedDiscount: { catalogDiscount: null, storeDiscount: null },
        applicableDiscounts: { catalogDiscounts: [baseCatalogDiscount], storeDiscounts: [] },
      },
    ]);
  });

  it('Should return store applicable discounts if not any amount matches and there is no catalog applicable discount', () => {
    const catalogDiscount: CatalogDiscountWithPriceAfterDiscount = { ...baseCatalogDiscount, catalogIds: ['2'] };
    const storeDiscount: StoreDiscountWithPriceAfterDiscount = { ...baseStoreDiscount, requiredAmount: 250 };
    const catalogItem: CatalogWithQuantity = { ...baseCatalogItem, quantity: 2 };

    (CatalogDiscountOperator.hasCatalogAccess as jest.Mock).mockReturnValue(false);
    (StoreDiscountOperator.hasRequiredAmount as jest.Mock).mockReturnValue(false);

    const result = service.calculateDiscountsForCatalogByClient({
      catalog: [catalogItem],
      catalogDiscounts: [catalogDiscount],
      storeDiscounts: [storeDiscount],
      client: baseClient,
    });

    expect(result).toEqual([
      {
        ...catalogItem,
        priceAfterDiscount: 100,
        appliedDiscount: { catalogDiscount: null, storeDiscount: null },
        applicableDiscounts: { catalogDiscounts: [], storeDiscounts: [storeDiscount] },
      },
    ]);
  });

  it('Should return multiple applicable catalog discounts if they are applicable', () => {
    const catalogDiscount2: CatalogDiscountWithPriceAfterDiscount = { ...baseCatalogDiscount, id: '2', requiredQuantity: 2 };

    const result = service.calculateDiscountsForCatalogByClient({
      catalog: [baseCatalogItem],
      catalogDiscounts: [baseCatalogDiscount, catalogDiscount2],
      storeDiscounts: [],
      client: baseClient,
    });

    expect(result).toEqual([
      {
        ...baseCatalogItem,
        priceAfterDiscount: 90,
        appliedDiscount: { catalogDiscount: catalogDiscount2, storeDiscount: null },
        applicableDiscounts: { catalogDiscounts: [baseCatalogDiscount, catalogDiscount2], storeDiscounts: [] },
      },
    ]);

    expect(CatalogDiscountOperator.isApplicable).toHaveBeenCalledTimes(2);
    expect(CatalogDiscountOperator.calculatePriceAfterDiscount).toHaveBeenCalledTimes(2);
    expect(CatalogDiscountOperator.hasRequiredQuantity).toHaveBeenCalledTimes(2);
    expect(CatalogDiscountOperator.hasCatalogAccess).toHaveBeenCalledTimes(2);
    expect(CatalogDiscountOperator.hasClientAccess).toHaveBeenCalledTimes(2);
    expect(CatalogDiscountOperator.hasGlobalAccess).toHaveBeenCalledTimes(2);
  });

  it('Should return multiple applicable store discounts if they are applicable', () => {
    const storeDiscount2: StoreDiscountWithPriceAfterDiscount = {
      ...baseStoreDiscount, id: '2', requiredAmount: 150, priceAfterDiscount: 90,
    };

    (StoreDiscountOperator.hasRequiredAmount as jest.Mock)
      .mockReturnValueOnce(true)
      .mockReturnValueOnce(false);

    (CatalogDiscountOperator.hasCatalogAccess as jest.Mock).mockReturnValue(false);

    (StoreDiscountOperator.calculatePriceAfterDiscount as jest.Mock)
      .mockReturnValueOnce(85)
      .mockReturnValueOnce(90);

    const result = service.calculateDiscountsForCatalogByClient({
      catalog: [baseCatalogItem],
      catalogDiscounts: [],
      storeDiscounts: [baseStoreDiscount, storeDiscount2],
      client: baseClient,
    });

    expect(result).toEqual([
      {
        ...baseCatalogItem,
        priceAfterDiscount: 85,
        appliedDiscount: { catalogDiscount: null, storeDiscount: baseStoreDiscount },
        applicableDiscounts: { catalogDiscounts: [], storeDiscounts: [baseStoreDiscount, storeDiscount2] },
      },
    ]);
  });

  it('Should apply near down discount depending on amount', () => {
    const catalogItem: CatalogWithQuantity = {
      ...baseCatalogItem, quantity: 4, id: '2', price: 400,
    };
    const storeDiscount2: StoreDiscountWithPriceAfterDiscount = { ...baseStoreDiscount, requiredAmount: 250, priceAfterDiscount: 360 };
    const storeDiscount3: StoreDiscountWithPriceAfterDiscount = { ...baseStoreDiscount, requiredAmount: 350, priceAfterDiscount: 340 };

    (CatalogDiscountOperator.hasCatalogAccess as jest.Mock).mockReturnValue(false);

    (StoreDiscountOperator.hasRequiredAmount as jest.Mock)
      .mockReturnValueOnce(true)
      .mockReturnValueOnce(true);

    (StoreDiscountOperator.calculatePriceAfterDiscount as jest.Mock)
      .mockReturnValueOnce(360)
      .mockReturnValueOnce(340);

    const result = service.calculateDiscountsForCatalogByClient({
      catalog: [catalogItem],
      catalogDiscounts: [baseCatalogDiscount],
      storeDiscounts: [storeDiscount2, storeDiscount3],
      client: baseClient,
    });

    expect(result).toEqual([
      {
        ...catalogItem,
        priceAfterDiscount: 340,
        appliedDiscount: { catalogDiscount: null, storeDiscount: storeDiscount3 },
        applicableDiscounts: { catalogDiscounts: [], storeDiscounts: [storeDiscount2, storeDiscount3] },
      },
    ]);
  });

  it('Should handle global catalog discounts correctly', () => {
    const catalogDiscountGlobal: CatalogDiscountWithPriceAfterDiscount = { ...baseCatalogDiscount, clientIds: [] };

    (CatalogDiscountOperator.hasGlobalAccess as jest.Mock).mockReturnValue(true);
    (CatalogDiscountOperator.hasClientAccess as jest.Mock).mockReturnValue(false);

    const result = service.calculateDiscountsForCatalogByClient({
      catalog: [baseCatalogItem],
      catalogDiscounts: [catalogDiscountGlobal],
      storeDiscounts: [],
      client: baseClient,
    });

    expect(result).toEqual([
      {
        ...baseCatalogItem,
        priceAfterDiscount: 90,
        appliedDiscount: { catalogDiscount: catalogDiscountGlobal, storeDiscount: null },
        applicableDiscounts: { catalogDiscounts: [catalogDiscountGlobal], storeDiscounts: [] },
      },
    ]);
  });

  it('Should return client catalog discount if global has same required quantity', () => {
    const catalogDiscountGlobal: CatalogDiscountWithPriceAfterDiscount = { ...baseCatalogDiscount, clientIds: [] };
    const catalogDiscountClient: CatalogDiscountWithPriceAfterDiscount = { ...baseCatalogDiscount, id: '2' };

    (CatalogDiscountOperator.hasGlobalAccess as jest.Mock)
      .mockReturnValueOnce(true)
      .mockReturnValueOnce(false);

    (CatalogDiscountOperator.hasClientAccess as jest.Mock)
      .mockReturnValueOnce(false)
      .mockReturnValueOnce(true);

    (CatalogDiscountOperator.hasRequiredQuantity as jest.Mock).mockReturnValue(true);

    const result = service.calculateDiscountsForCatalogByClient({
      catalog: [baseCatalogItem],
      catalogDiscounts: [catalogDiscountGlobal, catalogDiscountClient],
      storeDiscounts: [],
      client: baseClient,
    });

    expect(result).toEqual([
      {
        ...baseCatalogItem,
        priceAfterDiscount: 90,
        appliedDiscount: { catalogDiscount: catalogDiscountClient, storeDiscount: null },
        applicableDiscounts: { catalogDiscounts: [catalogDiscountClient], storeDiscounts: [] },
      },
    ]);
  });
});

describe('Catalog DomainService - calculateCatalogDiscountAndTaxes', () => {
  const baseCatalogItem: CatalogWithQuantity = {
    id: '1',
    price: 100,
    name: 'Item 1',
    attributes: [],
    companyId: '1',
    description: 'Description',
    disabledAt: null,
    discountIds: ['1'],
    inventoryRelations: [],
    readId: '1',
    requiresStock: false,
    taxIds: ['tax1'],
    type: CatalogType.PRODUCT,
    createdAt: new Date(),
    updatedAt: new Date(),
    quantity: 1,
    mediaIds: [],
  };

  const baseCatalogDiscount: CatalogDiscountWithPriceAfterDiscount = {
    id: '1',
    catalogIds: ['1'],
    clientIds: ['client1'],
    discountType: DiscountType.PERCENTAGE,
    discountValue: 10,
    priceAfterDiscount: 90,
    requiredQuantity: 0,
    companyId: '1',
    startDate: new Date(),
    endDate: new Date(),
    disabledAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    name: '10% catalog discount',
  };

  const baseStoreDiscount: StoreDiscountWithPriceAfterDiscount = {
    id: '1',
    clientIds: ['client1'],
    discountType: DiscountType.PERCENTAGE,
    discountValue: 15,
    priceAfterDiscount: 85,
    name: '15% store discount',
    requiredAmount: 0,
    companyId: '1',
    startDate: new Date(),
    endDate: new Date(),
    disabledAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const baseTax = {
    id: 'tax1',
    name: 'VAT',
    value: 16,
    type: TaxType.PERCENTAGE,
    countryCode: 'US',
    companyId: '1',
    disabledAt: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const baseClient: ClientEntity = {
    id: 'client1',
    clientCompanyId: '1',
    name: 'Client 1',
    companyId: '1',
    storeDiscounts: [],
    tributaryId: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(() => {
    jest.clearAllMocks();

    (CatalogDiscountOperator.hasCatalogAccess as jest.Mock).mockReturnValue(true);
    (CatalogDiscountOperator.hasRequiredQuantity as jest.Mock).mockReturnValue(true);
    (CatalogDiscountOperator.isApplicable as jest.Mock).mockReturnValue(true);
    (CatalogDiscountOperator.hasClientAccess as jest.Mock).mockReturnValue(true);
    (CatalogDiscountOperator.hasGlobalAccess as jest.Mock).mockReturnValue(false);
    (CatalogDiscountOperator.calculatePriceAfterDiscount as jest.Mock).mockReturnValue(90);

    (StoreDiscountOperator.hasRequiredAmount as jest.Mock).mockReturnValue(true);
    (StoreDiscountOperator.isApplicable as jest.Mock).mockReturnValue(true);
    (StoreDiscountOperator.hasClientAccess as jest.Mock).mockReturnValue(true);
    (StoreDiscountOperator.hasGlobalAccess as jest.Mock).mockReturnValue(false);
    (StoreDiscountOperator.calculatePriceAfterDiscount as jest.Mock).mockReturnValue(85);

    (TaxOperator.calculateTaxAmount as jest.Mock).mockReturnValue(14.4);

    // Simplified mock implementation that simply converts values to numbers
    (DomainUtil.toOperablePrice as jest.Mock).mockImplementation((value) => Number(value));
    (DomainUtil.toDisplayPrice as jest.Mock).mockImplementation((value) => Number(value));
  });

  it('applies taxes to a catalog item with discount', () => {
    // Override specific mocks for this test if needed
    (TaxOperator.calculateTaxAmount as jest.Mock).mockReturnValue(14.4);

    const result = service.calculateCatalogDiscountAndTaxes({
      catalog: [baseCatalogItem],
      catalogDiscounts: [baseCatalogDiscount],
      storeDiscounts: [],
      client: baseClient,
      taxes: [baseTax],
    });

    expect(result.length).toBe(1);
    expect(result[0]).toEqual({
      ...baseCatalogItem,
      priceAfterDiscount: 90,
      priceAfterTaxes: 104.4,
      appliedDiscount: { catalogDiscount: baseCatalogDiscount, storeDiscount: null },
      applicableDiscounts: { catalogDiscounts: [baseCatalogDiscount], storeDiscounts: [] },
      taxes: [{ ...baseTax, amount: 14.4 }],
    });

    expect(TaxOperator.calculateTaxAmount).toHaveBeenCalledWith(baseTax, 90);
  });

  it('applies taxes to a catalog item with store discount', () => {
    (CatalogDiscountOperator.hasCatalogAccess as jest.Mock).mockReturnValue(false);
    (TaxOperator.calculateTaxAmount as jest.Mock).mockReturnValue(13.6);

    const result = service.calculateCatalogDiscountAndTaxes({
      catalog: [baseCatalogItem],
      catalogDiscounts: [],
      storeDiscounts: [baseStoreDiscount],
      client: baseClient,
      taxes: [baseTax],
    });

    expect(result.length).toBe(1);
    expect(result[0]).toEqual({
      ...baseCatalogItem,
      priceAfterDiscount: 85,
      priceAfterTaxes: 98.6,
      appliedDiscount: { catalogDiscount: null, storeDiscount: baseStoreDiscount },
      applicableDiscounts: { catalogDiscounts: [], storeDiscounts: [baseStoreDiscount] },
      taxes: [{ ...baseTax, amount: 13.6 }],
    });

    expect(TaxOperator.calculateTaxAmount).toHaveBeenCalledWith(baseTax, 85);
  });

  it('applies taxes to a catalog item with no discount', () => {
    (CatalogDiscountOperator.hasCatalogAccess as jest.Mock).mockReturnValue(false);
    (StoreDiscountOperator.isApplicable as jest.Mock).mockReturnValue(false);
    (TaxOperator.calculateTaxAmount as jest.Mock).mockReturnValue(16);

    const result = service.calculateCatalogDiscountAndTaxes({
      catalog: [baseCatalogItem],
      catalogDiscounts: [],
      storeDiscounts: [],
      client: baseClient,
      taxes: [baseTax],
    });

    expect(result.length).toBe(1);
    expect(result[0]).toEqual({
      ...baseCatalogItem,
      priceAfterDiscount: 100,
      priceAfterTaxes: 116,
      appliedDiscount: { catalogDiscount: null, storeDiscount: null },
      applicableDiscounts: { catalogDiscounts: [], storeDiscounts: [] },
      taxes: [{ ...baseTax, amount: 16 }],
    });

    expect(TaxOperator.calculateTaxAmount).toHaveBeenCalledWith(baseTax, 100);
  });

  it('applies multiple taxes to a catalog item', () => {
    const secondTax = {
      id: 'tax2',
      name: 'City Tax',
      value: 5,
      type: TaxType.PERCENTAGE,
      countryCode: 'US',
      companyId: '1',
      disabledAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const catalogItemWithMultipleTaxes = {
      ...baseCatalogItem,
      taxIds: ['tax1', 'tax2'],
    };

    (TaxOperator.calculateTaxAmount as jest.Mock)
      .mockReturnValueOnce(14.4) // 16% VAT
      .mockReturnValueOnce(4.5); // 5% City Tax

    const result = service.calculateCatalogDiscountAndTaxes({
      catalog: [catalogItemWithMultipleTaxes],
      catalogDiscounts: [baseCatalogDiscount],
      storeDiscounts: [],
      client: baseClient,
      taxes: [baseTax, secondTax],
    });

    expect(result.length).toBe(1);
    expect(result[0]).toEqual({
      ...catalogItemWithMultipleTaxes,
      priceAfterDiscount: 90,
      priceAfterTaxes: 108.9,
      appliedDiscount: { catalogDiscount: baseCatalogDiscount, storeDiscount: null },
      applicableDiscounts: { catalogDiscounts: [baseCatalogDiscount], storeDiscounts: [] },
      taxes: [
        { ...baseTax, amount: 14.4 },
        { ...secondTax, amount: 4.5 },
      ],
    });

    expect(TaxOperator.calculateTaxAmount).toHaveBeenCalledWith(baseTax, 90);
    expect(TaxOperator.calculateTaxAmount).toHaveBeenCalledWith(secondTax, 90);
  });

  it('handles multiple catalog items with taxes and discounts', () => {
    const secondCatalogItem = {
      ...baseCatalogItem,
      id: '2',
      price: 200,
      taxIds: ['tax1'],
    };

    const secondCatalogDiscount = {
      ...baseCatalogDiscount,
      id: '2',
      catalogIds: ['2'],
      priceAfterDiscount: 180,
    };

    (CatalogDiscountOperator.hasCatalogAccess as jest.Mock)
      .mockImplementation((discount, catalogId) => discount.catalogIds.includes(catalogId));

    (CatalogDiscountOperator.calculatePriceAfterDiscount as jest.Mock)
      .mockReturnValueOnce(90)
      .mockReturnValueOnce(180);

    (TaxOperator.calculateTaxAmount as jest.Mock)
      .mockReturnValueOnce(14.4) // 16% of 90
      .mockReturnValueOnce(28.8); // 16% of 180

    const result = service.calculateCatalogDiscountAndTaxes({
      catalog: [baseCatalogItem, secondCatalogItem],
      catalogDiscounts: [baseCatalogDiscount, secondCatalogDiscount],
      storeDiscounts: [],
      client: baseClient,
      taxes: [baseTax],
    });

    expect(result.length).toBe(2);
    expect(result[0]).toEqual({
      ...baseCatalogItem,
      priceAfterDiscount: 90,
      priceAfterTaxes: 104.4,
      appliedDiscount: { catalogDiscount: baseCatalogDiscount, storeDiscount: null },
      applicableDiscounts: { catalogDiscounts: [baseCatalogDiscount], storeDiscounts: [] },
      taxes: [{ ...baseTax, amount: 14.4 }],
    });

    expect(result[1]).toEqual({
      ...secondCatalogItem,
      priceAfterDiscount: 180,
      priceAfterTaxes: 208.8,
      appliedDiscount: { catalogDiscount: secondCatalogDiscount, storeDiscount: null },
      applicableDiscounts: { catalogDiscounts: [secondCatalogDiscount], storeDiscounts: [] },
      taxes: [{ ...baseTax, amount: 28.8 }],
    });
  });

  it('does not apply taxes if catalog item has no tax IDs', () => {
    const catalogItemWithoutTaxes = {
      ...baseCatalogItem,
      taxIds: [],
    };

    const result = service.calculateCatalogDiscountAndTaxes({
      catalog: [catalogItemWithoutTaxes],
      catalogDiscounts: [baseCatalogDiscount],
      storeDiscounts: [],
      client: baseClient,
      taxes: [baseTax],
    });

    expect(result.length).toBe(1);
    expect(result[0]).toEqual({
      ...catalogItemWithoutTaxes,
      priceAfterDiscount: 90,
      priceAfterTaxes: 90,
      appliedDiscount: { catalogDiscount: baseCatalogDiscount, storeDiscount: null },
      applicableDiscounts: { catalogDiscounts: [baseCatalogDiscount], storeDiscounts: [] },
      taxes: [],
    });

    expect(TaxOperator.calculateTaxAmount).not.toHaveBeenCalled();
  });

  it('does not apply taxes if they do not exist in the provided taxes array', () => {
    const catalogItemWithNonExistentTax = {
      ...baseCatalogItem,
      taxIds: ['nonexistenttax'],
    };

    const result = service.calculateCatalogDiscountAndTaxes({
      catalog: [catalogItemWithNonExistentTax],
      catalogDiscounts: [baseCatalogDiscount],
      storeDiscounts: [],
      client: baseClient,
      taxes: [baseTax],
    });

    expect(result.length).toBe(1);
    expect(result[0]).toEqual({
      ...catalogItemWithNonExistentTax,
      priceAfterDiscount: 90,
      priceAfterTaxes: 90,
      appliedDiscount: { catalogDiscount: baseCatalogDiscount, storeDiscount: null },
      applicableDiscounts: { catalogDiscounts: [baseCatalogDiscount], storeDiscounts: [] },
      taxes: [],
    });

    expect(TaxOperator.calculateTaxAmount).not.toHaveBeenCalled();
  });
});
