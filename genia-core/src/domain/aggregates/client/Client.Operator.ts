import ClientEntity from '#domain/aggregates/client/Client.Entity';
import { Modify } from '#domain/common/Common.Type';

export type clientBuildParams = Modify<
ClientEntity,
{
  tributaryId?: string | null
  clientCompanyId?: string | null
  storeDiscounts?: string[]
  createdAt?: Date | null;
  updatedAt?: Date | null;
}
>;

export type clientUpdateParams = {
  name?: string;
  tributaryId?: string | null;
  storeDiscounts?: string[];
  createdAt?: Date | null;
  updatedAt?: Date | null;
};

function build(params: clientBuildParams): ClientEntity {
  const tributaryId = params.tributaryId || null;
  const clientCompanyId = params.clientCompanyId || null;
  const storeDiscounts = params.storeDiscounts || [];
  const createdAt = params.createdAt || new Date();
  const updatedAt = params.createdAt || new Date();

  const client: ClientEntity = {
    companyId: params.companyId,
    createdAt,
    updatedAt,
    id: params.id,
    name: params.name,
    tributaryId,
    clientCompanyId,
    storeDiscounts,
  };

  return client;
}

function update(client: ClientEntity, params: clientUpdateParams): ClientEntity {
  return build({
    ...client,
    ...params,
  });
}

export default {
  build,
  update,
};
