import ClientEntity from '#domain/aggregates/client/Client.Entity';
import ClientOperator, { clientBuildParams, clientUpdateParams } from '#domain/aggregates/client/Client.Operator';

jest.unmock('./Client.Operator');

describe('ClientOperator', () => {
  describe('build', () => {
    it('Should return a client with default values', () => {
      const params: clientBuildParams = {
        id: 'cli_123',
        name: 'kavak',
        companyId: 'comp_123',
      };

      const expected: ClientEntity = {
        ...params,
        tributaryId: null,
        clientCompanyId: null,
        storeDiscounts: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const got = ClientOperator.build(params);

      expect(got).toStrictEqual(expected);
    });

    it('Should return a client with custom values', () => {
      const params: clientBuildParams = {
        id: 'cli_123',
        name: 'kavak',
        tributaryId: '*********',
        companyId: 'comp_123',
        clientCompanyId: 'comp_124',
        storeDiscounts: ['sd123'],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const got = ClientOperator.build(params);

      expect(got).toStrictEqual(params);
    });
  });

  describe('update', () => {
    it('should update a client correctly', () => {
      const client: ClientEntity = {
        id: 'cli_123',
        name: 'China Parts On Fire',
        tributaryId: '*********',
        clientCompanyId: null,
        companyId: 'comp_123',
        storeDiscounts: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const params: clientUpdateParams = {
        storeDiscounts: ['sd_123'],
      };

      const updated = ClientOperator.update(client, params);

      expect(updated).toEqual({ ...client, ...params });
    });
  });
});
