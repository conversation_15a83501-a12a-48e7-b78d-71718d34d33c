import ProviderEntity from '#domain/aggregates/provider/Provider.Entity';
import { Modify } from '#domain/common/Common.Type';

export type providerBuildParams = Modify<
ProviderEntity,
{
  tributaryId?: string | null;
  providerCompanyId?: string | null;
}
>;

function build(params: providerBuildParams): ProviderEntity {
  const tributaryId = params.tributaryId || null;
  const providerCompanyId = params.providerCompanyId || null;

  return {
    ...params, tributaryId, providerCompanyId,
  };
}

function update(provider: ProviderEntity, toUpdate: Partial<Omit<ProviderEntity, 'id'>>): ProviderEntity {
  return {
    ...provider, ...toUpdate,
  };
}

export default {
  build,
  update,
};
