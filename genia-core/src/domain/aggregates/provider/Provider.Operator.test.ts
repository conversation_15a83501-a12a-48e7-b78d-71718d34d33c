import ProviderEntity from '#domain/aggregates/provider/Provider.Entity';
import ProviderOperator, { providerBuildParams } from '#domain/aggregates/provider/Provider.Operator';

jest.unmock('./Provider.Operator');

describe('ProviderOperator', () => {
  describe('build', () => {
    it('Should build a provider correctly with provided vaules', () => {
      const params: providerBuildParams = {
        id: 'prov_123',
        name: 'China Parts On Fire',
        companyId: 'comp_123',
        tributaryId: '*********',
        providerCompanyId: 'comp_123',
      };

      const expected: ProviderEntity = {
        ...params,
        tributaryId: '*********',
        providerCompanyId: 'comp_123',
      };

      const got = ProviderOperator.build(params);

      expect(got).toStrictEqual(expected);
    });

    it('Should build a provider correctly with default vaules', () => {
      const params: providerBuildParams = {
        id: 'prov_123',
        name: 'China Parts On Fire',
        companyId: 'comp_123',
      };

      const expected: ProviderEntity = {
        ...params,
        tributaryId: null,
        providerCompanyId: null,
      };

      const got = ProviderOperator.build(params);

      expect(got).toStrictEqual(expected);
    });
  });

  describe('update', () => {
    it('should return the updated provider', () => {
      const provider: ProviderEntity = {
        id: 'prov_123',
        name: 'China Parts On Fire',
        companyId: 'comp_123',
        tributaryId: '*********',
        providerCompanyId: 'comp_123',
      };

      const updates = {
        tributaryId: '*********',
      };

      const expected: ProviderEntity = {
        ...provider,
        ...updates,
      };

      const got = ProviderOperator.update(provider, updates);

      expect(got).toEqual(expected);
    });
  });
});
