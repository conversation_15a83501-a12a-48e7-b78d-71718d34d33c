import CompanyEntity from '#domain/aggregates/company/Company.Entity';

import CompanyOperator from './Company.Operator';

jest.unmock('./Company.Operator');

describe('Company Operator', () => {
  describe('build', () => {
    it('should create a company object with all required fields', () => {
      const companyParams: CompanyEntity = {
        id: '123',
        name: 'Test Company',
        description: 'A test company description',
        country: 'US',
        tributaryId: '12345678',
      };

      const result = CompanyOperator.build(companyParams);

      expect(result).toEqual({
        id: '123',
        name: 'Test Company',
        description: 'A test company description',
        country: 'US',
        tributaryId: '12345678',
      });
    });

    it('should preserve all fields from the input params', () => {
      const companyParams: CompanyEntity = {
        id: 'company-id-456',
        name: 'Another Company',
        description: 'Another description',
        country: 'CA',
        tributaryId: '87654321',
      };

      const result = CompanyOperator.build(companyParams);

      expect(result).toEqual(companyParams);
    });

    it('should handle empty description', () => {
      const companyParams: CompanyEntity = {
        id: 'company-id-789',
        name: 'Company with empty description',
        description: '',
        country: 'MX',
        tributaryId: '11111111',
      };

      const result = CompanyOperator.build(companyParams);

      expect(result).toEqual(companyParams);
      expect(result.description).toBe('');
    });
  });
});
