import CreateClientEntriesCommand from '#application/client/commands/CreateClientEntries.Command';
import UpdateClientEntryCommand from '#application/client/commands/UpdateClientEntry.Command';
import ClientUseCase from '#application/client/useCases/Client.UseCase';
import Registry from '#composition/ImplementationRegistry';
import ClientEntity from '#domain/aggregates/client/Client.Entity';
import ClientOperator from '#domain/aggregates/client/Client.Operator';

jest.unmock('./Client.UseCase');
jest.unmock('lodash');

describe('ClientUseCase', () => {
  const mockDate = new Date('2023-01-01');

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    jest.setSystemTime(mockDate);
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('createClientEntries', () => {
    const command: CreateClientEntriesCommand = {
      entries: [{
        name: 'China Parts On Fire',
        tributaryId: '*********',
        clientCompanyId: 'prov_123',
      }],
      companyId: 'prov_124',
    };

    const commandWithStoreDiscounts: CreateClientEntriesCommand = {
      entries: command.entries.map((entry) => ({ ...entry, storeDiscounts: ['sd_123', 'sd_1234'] })),
      companyId: 'prov_124',
    };

    const client: ClientEntity = {
      id: 'prov123',
      name: command.entries[0].name,
      tributaryId: '*********',
      clientCompanyId: 'prov_123',
      companyId: command.companyId,
      storeDiscounts: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('Should create new clients', async () => {
      (Registry.IdentificationService.generateId as jest.Mock).mockResolvedValueOnce('prov123');
      (Registry.ClientRepository.saveMany as jest.Mock).mockResolvedValueOnce(client);
      (Registry.CompanyRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([{ id: 'prov_123' }]);
      (ClientOperator.build as jest.Mock).mockReturnValueOnce(client);

      const gotClient = await ClientUseCase.createClientEntries(command);

      expect(gotClient).toEqual(client);
      expect(Registry.IdentificationService.generateId).toHaveBeenCalled();
      expect(ClientOperator.build).toHaveBeenCalledWith({ ...command.entries[0], id: 'prov123', companyId: command.companyId });
      expect(Registry.ClientRepository.saveMany).toHaveBeenCalledWith([client]);
    });

    it('Should create new clients with storeDiscounts', async () => {
      (Registry.IdentificationService.generateId as jest.Mock).mockResolvedValueOnce('prov123');
      (Registry.ClientRepository.saveMany as jest.Mock).mockResolvedValueOnce(client);
      (Registry.CompanyRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([{ id: 'prov_123' }]);
      (Registry.StoreDiscountRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([{ id: 'sd_123' }, { id: 'sd_1234' }]);
      (ClientOperator.build as jest.Mock).mockReturnValueOnce(client);

      const gotClient = await ClientUseCase.createClientEntries(commandWithStoreDiscounts);

      expect(gotClient).toEqual(client);
      expect(Registry.IdentificationService.generateId).toHaveBeenCalled();
      expect(Registry.StoreDiscountRepository.findManyByIds).toHaveBeenCalledWith(['sd_123', 'sd_1234']);
      expect(ClientOperator.build).toHaveBeenCalledWith({ ...commandWithStoreDiscounts.entries[0], id: 'prov123', companyId: command.companyId });
      expect(Registry.ClientRepository.saveMany).toHaveBeenCalledWith([client]);
    });

    it('Should throw an error when storeDiscounts are not found', async () => {
      (Registry.StoreDiscountRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([{ id: 'sd_123' }]);

      expect(ClientUseCase.createClientEntries(commandWithStoreDiscounts)).rejects.toThrow('Some storeDiscounts were not found: sd_1234');
    });

    it('Should create new clients with no clientCompanyIds', async () => {
      const testCommand: CreateClientEntriesCommand = {
        entries: [{
          name: 'China Parts On Fire',
          tributaryId: '*********',
        }],
        companyId: 'prov_124',
      };

      (Registry.IdentificationService.generateId as jest.Mock).mockResolvedValueOnce('prov123');
      (Registry.ClientRepository.saveMany as jest.Mock).mockResolvedValueOnce(client);
      (ClientOperator.build as jest.Mock).mockReturnValueOnce(client);

      const gotClient = await ClientUseCase.createClientEntries(testCommand);

      expect(gotClient).toEqual(client);
      expect(Registry.IdentificationService.generateId).toHaveBeenCalled();
      expect(ClientOperator.build).toHaveBeenCalledWith({ ...testCommand.entries[0], id: 'prov123', companyId: command.companyId });
      expect(Registry.ClientRepository.saveMany).toHaveBeenCalledWith([client]);
    });

    it('Should throw an error when clientCompanyIds are not found', async () => {
      (Registry.CompanyRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([]);

      expect(ClientUseCase.createClientEntries(command)).rejects.toThrow(`Some company were not found: ${command.entries[0].clientCompanyId}`);
    });
  });

  describe('updateClientEntry', () => {
    const command: UpdateClientEntryCommand = {
      id: 'cli_123',
      companyId: 'comp_123',
      name: 'China Parts Not On Fire',
      storeDiscounts: ['sd_123', 'sd_1234'],
    };

    const mockClient: ClientEntity = {
      id: command.id,
      companyId: command.companyId,
      name: 'China Parts Not On Fire',
      storeDiscounts: ['sd_1234'],
      tributaryId: null,
      clientCompanyId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should throw an error when the client is not found', async () => {
      expect(ClientUseCase.updateClientEntry(command)).rejects.toThrow('Client not found');
    });

    it('should throw an error when the client does not belong to the provided company', async () => {
      (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValueOnce({ companyId: 'wrong_uuid' });

      expect(ClientUseCase.updateClientEntry(command)).rejects.toThrow('Forbidden');
    });

    it('should throw an error when the store discounts are not found', async () => {
      (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockClient);
      (Registry.StoreDiscountRepository.findManyByIds as jest.Mock).mockResolvedValueOnce(['sd_123']);

      expect(ClientUseCase.updateClientEntry(command)).rejects.toThrow('Some storeDiscounts were not found: sd_123');
    });

    it('should update a client correctly', async () => {
      const commandWithNoStoreDiscounts: UpdateClientEntryCommand = {
        id: command.id,
        companyId: command.companyId,
        name: command.name,
      };

      (Registry.ClientRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockClient);
      (ClientOperator.update as jest.Mock).mockReturnValueOnce(mockClient);
      (Registry.ClientRepository.save as jest.Mock).mockResolvedValueOnce(mockClient);

      const got = await ClientUseCase.updateClientEntry(commandWithNoStoreDiscounts);

      expect(got).toEqual(mockClient);
      expect(Registry.ClientRepository.findOneById).toHaveBeenCalledWith(command.id);
      expect(ClientOperator.update).toHaveBeenCalledWith(mockClient, commandWithNoStoreDiscounts);
      expect(Registry.ClientRepository.save).toHaveBeenCalledWith(mockClient);
    });
  });
});
