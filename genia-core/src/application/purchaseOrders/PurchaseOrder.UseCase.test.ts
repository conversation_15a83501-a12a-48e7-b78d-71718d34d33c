import OrderUseCase from '#application/common/order/Order.UseCase';
import { OrderItemCalculated } from '#application/common/order/results/CalculateOrderConditions.Result';
import CalculatePurchaseOrderConditionsCommand from '#application/purchaseOrders/commands/CalculatePurchaseOrderConditions.Command';
import CreatePurchaseOrderEntriesCommand from '#application/purchaseOrders/commands/CreatePurchaseOrderEntriesCommand';
import Registry from '#composition/ImplementationRegistry';
import CatalogEntity, { CatalogType } from '#domain/aggregates/catalog/Catalog.Entity';
import ClientObject from '#domain/aggregates/client/Client.Entity';
import ProviderObject from '#domain/aggregates/provider/Provider.Entity';
import PurchaseOrderEntity, { PurchaseOrderStatus } from '#domain/aggregates/purchaseOrder/PurchaseOrder.Entity';
import PurchaseOrderOperator from '#domain/aggregates/purchaseOrder/PurchaseOrder.Operator';
import SaleOrderEntity, { SaleOrderStatus } from '#domain/aggregates/saleOrder/SaleOrder.Entity';
import SaleOrderOperator from '#domain/aggregates/saleOrder/SaleOrder.Operator';
import StoreDiscountEntity from '#domain/aggregates/storeDiscount/StoreDiscount.Entity';
import { TaxType } from '#domain/aggregates/tax/Tax.Entity';
import TaxOperator from '#domain/aggregates/tax/Tax.Operator';
import { DiscountType } from '#domain/common/aggregates/discount/Discount.Entity';
import { CatalogCalculatedTax, CatalogWithDiscount, CatalogWithTaxes } from '#domain/domainServices/Catalog.DomainService';
import OrderDomainService from '#domain/domainServices/Order.DomainService';
import OrderConditionsDomainService, {
  CalculateConditionsParams,
  OrderConditions, OrderItemConditions,
} from '#domain/domainServices/OrderConditions.DomainService';

import PurchaseOrderUseCase from './PurchaseOrder.UseCase';

interface PurchaseOrderItemConditions extends Omit<OrderItemConditions, 'catalogId'> {
  referenceId: string;
  subtotal: number;
  total: number;
}

interface PurchaseOrderConditions extends Omit<OrderConditions, 'orderItems'> {
  orderItems: PurchaseOrderItemConditions[];
}

jest.unmock('./PurchaseOrder.UseCase');

describe('PurchaseOrderUseCase', () => {
  const mockClient: ClientObject = {
    id: 'client-id',
    companyId: 'provider-company-id',
    clientCompanyId: 'company-id',
    name: 'Test Client',
    tributaryId: '*********',
    storeDiscounts: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockProvider: ProviderObject = {
    id: 'provider-id',
    providerCompanyId: 'provider-company-id',
    companyId: 'company-id',
    name: 'Test Provider',
    tributaryId: '*********',
  };

  const mockProviderWithoutCompany: ProviderObject = {
    id: 'provider-id',
    providerCompanyId: null,
    name: 'Test Provider Without Company',
    companyId: 'company-id',
    tributaryId: '*********',
  };

  const mockCatalogItem: CatalogEntity = {
    id: 'catalog-item-1',
    price: 100,
    taxIds: ['tax-1'],
    companyId: 'provider-company-id',
    readId: 'SKU-1',
    requiresStock: true,
    name: 'Test Item',
    attributes: [],
    createdAt: new Date(),
    description: 'Test Description',
    disabledAt: null,
    inventoryRelations: [],
    mediaIds: [],
    type: CatalogType.PRODUCT,
    updatedAt: new Date(),
    discountIds: [],
  };

  const mockTax: CatalogCalculatedTax = {
    id: 'tax-1',
    name: 'VAT',
    amount: 16,
    type: TaxType.PERCENTAGE,
    disabledAt: null,
    countryCode: 'MX',
    createdAt: new Date(),
    updatedAt: new Date(),
    value: 14.4,
  };

  const mockStoreDiscount: StoreDiscountEntity = {
    id: 'discount-1',
    discountValue: 10,
    name: 'Store Discount',
    endDate: null,
    startDate: new Date(),
    discountType: DiscountType.PERCENTAGE,
    requiredAmount: 100,
    clientIds: ['client-id'],
    companyId: 'company-id',
    createdAt: new Date(),
    updatedAt: new Date(),
    disabledAt: null,
  };

  const mockCalculatedCatalogItem: CatalogWithDiscount & CatalogWithTaxes = {
    ...mockCatalogItem,
    quantity: 2,
    priceAfterDiscount: 90,
    priceAfterTaxes: 104.4,
    appliedDiscount: {
      catalogDiscount: null,
      storeDiscount: {
        ...mockStoreDiscount,
        priceAfterDiscount: 90,
      },
    },
    applicableDiscounts: {
      catalogDiscounts: [],
      storeDiscounts: [{
        ...mockStoreDiscount,
        priceAfterDiscount: 90,
      }],
    },
    taxes: [mockTax],
  };

  const mockOrderItemCalculated: OrderItemCalculated = {
    catalogId: 'catalog-item-1',
    quantity: 2,
    unitPrice: mockCatalogItem.price,
    unitPriceAfterDiscount: mockCalculatedCatalogItem.priceAfterDiscount,
    unitPriceAfterDiscountAndTaxes: mockCalculatedCatalogItem.priceAfterTaxes,
    discounts: {
      appliedDiscount: {
        catalogDiscount: null,
        storeDiscount: {
          ...mockStoreDiscount,
          priceAfterDiscount: 90,
        },
      },
      applicableDiscounts: {
        catalogDiscounts: [],
        storeDiscounts: [{
          ...mockStoreDiscount,
          priceAfterDiscount: 90,
        }],
      },
    },
    taxes: [{
      id: mockTax.id,
      name: mockTax.name,
      amount: mockTax.amount,
      type: mockTax.type,
      value: mockTax.value,
    }],
  };

  const mockOrderConditions: PurchaseOrderConditions = {
    subtotal: 180,
    subtotalBeforeDiscount: 200,
    totalDiscount: 20,
    totalTaxes: 28.8,
    total: 208.8,
    shippingPrice: 15,
    taxes: [{
      id: mockTax.id,
      name: mockTax.name,
      amount: mockTax.amount,
      type: mockTax.type,
      value: mockTax.value,
    }],
    hasTaxOnShipping: true,
    orderItems: [
      {
        referenceId: 'catalog-item-1',
        quantity: mockCalculatedCatalogItem.quantity,
        unitPrice: mockCalculatedCatalogItem.price,
        unitPriceAfterDiscountAndTaxes: 104.4,
        unitPriceAfterDiscount: mockCalculatedCatalogItem.appliedDiscount?.storeDiscount?.priceAfterDiscount as number,
        subtotal: 180,
        total: 208.8,
        taxes: [{
          id: mockTax.id,
          name: mockTax.name,
          amount: mockTax.amount,
          type: mockTax.type,
          value: mockTax.value,
        }],
      },
    ],
  };

  const defaultCommand = {
    companyId: 'company-id',
    providerId: 'provider-id',
    orderItems: [
      {
        referenceId: 'catalog-item-1',
        quantity: 2,
      },
    ],
    shippingPrice: 15, // Added shipping price
  };

  const mockManualOrderConditions: PurchaseOrderConditions = {
    orderItems: [
      {
        referenceId: 'manual-item-1',
        quantity: mockCalculatedCatalogItem.quantity,
        subtotal: 220,
        total: 255.2,
        unitPrice: mockCalculatedCatalogItem.price,
        taxes: [
          {
            id: mockTax.id,
            name: mockTax.name,
            amount: mockTax.amount,
            type: mockTax.type,
            value: mockTax.value,
          },
        ],
        unitPriceAfterDiscount: mockCalculatedCatalogItem.appliedDiscount?.storeDiscount?.priceAfterDiscount as number,
        unitPriceAfterDiscountAndTaxes: 127.2,
      },
    ],
    subtotal: 220,
    subtotalBeforeDiscount: 220,
    shippingPrice: 15,
    hasTaxOnShipping: true,
    taxes: [{
      id: mockTax.id,
      name: mockTax.name,
      amount: mockTax.amount,
      type: mockTax.type,
      value: mockTax.value,
    }],
    totalDiscount: 0,
    totalTaxes: 35.2,
    total: 255.2,
  };

  const manualOrderCommand = {
    companyId: 'company-id',
    providerId: 'provider-id',
    shippingPrice: 15,
    orderItems: [
      {
        referenceId: 'manual-item-1',
        quantity: 2,
        unitPrice: 120,
        unitPriceAfterDiscount: 110,
        unitPriceAfterDiscountAndTaxes: 127.2,
        taxIds: ['tax-1'],
      },
    ],
  };

  beforeEach(() => {
    jest.clearAllMocks();

    (Registry.ClientRepository.findManyByClientCompanyIds as jest.Mock).mockResolvedValue([mockClient]);
    (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValue(mockProvider);
    (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogItem]);
    (Registry.TaxRepository.findManyByIds as jest.Mock).mockResolvedValue([mockTax]);
    (Registry.CatalogDiscountRepository.findManyForCompaniesByCatalogIdsAndClientIds as jest.Mock).mockResolvedValue([]);
    (Registry.StoreDiscountRepository.findManyForCompaniesByClientIds as jest.Mock).mockResolvedValue([]);
    (OrderUseCase.getCatalogConditions as jest.Mock).mockReturnValue([mockOrderItemCalculated]);
    (OrderConditionsDomainService.calculateConditions as jest.Mock).mockReturnValue(mockOrderConditions);
    (TaxOperator.calculateTaxAmount as jest.Mock).mockReturnValue(104.4);
  });

  describe('calculatePurchaseOrderConditions', () => {
    it('Should throw if order items have zero length', async () => {
      const emptyCommand: CalculatePurchaseOrderConditionsCommand = {
        companyId: 'company-id',
        providerId: 'provider-id',
        orderItems: [],
      };

      await expect(
        PurchaseOrderUseCase.calculatePurchaseOrderConditions(emptyCommand),
      ).rejects.toThrow('Order items not found');
    });

    it('should calculate order conditions for catalog items', async () => {
      const result = await PurchaseOrderUseCase.calculatePurchaseOrderConditions(defaultCommand);

      expect(Registry.ProviderRepository.findOneById).toHaveBeenCalledWith('provider-id');
      expect(Registry.ClientRepository.findManyByClientCompanyIds).toHaveBeenCalledWith(['company-id']);
      expect(OrderUseCase.getCatalogConditions).toHaveBeenCalledWith(
        defaultCommand.orderItems.map(({ referenceId, ...item }) => ({
          ...item,
          catalogId: referenceId,
        })),
        mockProvider.providerCompanyId,
        mockClient,
      );
      expect(OrderConditionsDomainService.calculateConditions).toHaveBeenCalledWith({
        orderItems: [mockOrderItemCalculated],
        shippingPrice: 15,
      });

      expect(result).toEqual({
        subtotal: 180,
        subtotalBeforeDiscount: 200,
        totalDiscount: 20,
        totalTaxes: 28.8,
        total: 208.8,
        shippingPrice: 15,
        hasTaxOnShipping: true,
        taxes: [{
          id: mockTax.id,
          name: mockTax.name,
          amount: mockTax.amount,
          type: mockTax.type,
          value: mockTax.value,
        }],
        orderItems: [expect.objectContaining({
          referenceId: 'catalog-item-1',
          quantity: 2,
          unitPrice: mockCatalogItem.price,
          unitPriceAfterDiscountAndTaxes: 104.4,
          unitPriceAfterDiscount: mockCalculatedCatalogItem.appliedDiscount?.storeDiscount?.priceAfterDiscount as number,
          subtotal: 180,
          total: 208.8,
          taxes: expect.any(Array),
        })],
      });
    });

    it('should use default shipping price value when not provided', async () => {
      const commandWithoutShippingPrice = {
        companyId: 'company-id',
        providerId: 'provider-id',
        orderItems: [
          {
            referenceId: 'catalog-item-1',
            quantity: 2,
          },
        ],
      };

      await PurchaseOrderUseCase.calculatePurchaseOrderConditions(commandWithoutShippingPrice);

      expect(OrderConditionsDomainService.calculateConditions).toHaveBeenCalledWith({
        orderItems: expect.any(Array),
        shippingPrice: 0,
      });
    });

    it('should throw error when client not found', async () => {
      (Registry.ClientRepository.findManyByClientCompanyIds as jest.Mock).mockResolvedValueOnce([]);

      await expect(
        PurchaseOrderUseCase.calculatePurchaseOrderConditions(defaultCommand),
      ).rejects.toThrow('Client not found');
    });

    it('should throw error when provider not found', async () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(null);

      await expect(
        PurchaseOrderUseCase.calculatePurchaseOrderConditions(defaultCommand),
      ).rejects.toThrow('Provider provider-id not found');
    });

    it('should calculate conditions for manual items when provider has no company', async () => {
      const providerWithoutCompany = {
        ...mockProvider,
        providerCompanyId: null,
      };

      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(providerWithoutCompany);

      (OrderConditionsDomainService.calculateConditions as jest.Mock).mockReturnValueOnce(mockManualOrderConditions);

      const result = await PurchaseOrderUseCase.calculatePurchaseOrderConditions(manualOrderCommand);

      expect(Registry.ProviderRepository.findOneById).toHaveBeenCalledWith('provider-id');
      expect(Registry.ClientRepository.findManyByClientCompanyIds).not.toHaveBeenCalled();
      expect(Registry.TaxRepository.findManyByIds).toHaveBeenCalledWith(['tax-1']);

      expect(OrderConditionsDomainService.calculateConditions).toHaveBeenCalledWith(
        {
          shippingPrice: 15,
          orderItems: [{
            catalogId: 'manual-item-1',
            quantity: 2,
            discounts: null,
            unitPrice: 120,
            unitPriceAfterDiscount: 110,
            unitPriceAfterDiscountAndTaxes: 214.4,
            taxes: [expect.objectContaining({
              id: 'tax-1',
              name: 'VAT',
            })],
          }],
        },
      );

      expect(result).toEqual({
        subtotal: 220,
        subtotalBeforeDiscount: 220,
        totalDiscount: 0,
        totalTaxes: 35.2,
        total: 255.2,
        shippingPrice: 15,
        hasTaxOnShipping: true,
        taxes: [{
          id: 'tax-1',
          name: 'VAT',
          amount: 16,
          type: 'percentage',
          value: 14.4,
        }],
        orderItems: [
          expect.objectContaining({
            referenceId: 'manual-item-1',
            quantity: 2,
            unitPrice: 100,
            unitPriceAfterDiscountAndTaxes: 127.2,
            unitPriceAfterDiscount: 90,
            subtotal: 220,
            total: 255.2,
            taxes: expect.any(Array),
          }),
        ],
      });
    });

    it('should throw error when manual item has no unitPrice', async () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockProviderWithoutCompany);

      const invalidCommand = {
        ...manualOrderCommand,
        orderItems: [
          {
            referenceId: 'manual-item-1',
            quantity: 2,
            unitPriceAfterDiscount: 110,
            taxIds: ['tax-1'],
          },
        ],
      };

      await expect(
        PurchaseOrderUseCase.calculatePurchaseOrderConditions(invalidCommand),
      ).rejects.toThrow('Manual order item should have unitPrice greater than 0');
    });

    it('should throw error when manual item has no unitPriceAfterDiscount', async () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockProviderWithoutCompany);

      const invalidCommand = {
        ...manualOrderCommand,
        orderItems: [
          {
            referenceId: 'manual-item-1',
            quantity: 2,
            unitPrice: 120,
            taxIds: ['tax-1'],
          },
        ],
      };

      await expect(
        PurchaseOrderUseCase.calculatePurchaseOrderConditions(invalidCommand),
      ).rejects.toThrow('Manual order item should have unitPriceAfterDiscount greater than 0');
    });

    it('should throw error when tax not found for manual item', async () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockProviderWithoutCompany);
      (Registry.TaxRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([]);

      await expect(
        PurchaseOrderUseCase.calculatePurchaseOrderConditions(manualOrderCommand),
      ).rejects.toThrow('Tax tax-1 not found');
    });

    it('Should handle undefined taxIds for manual items', async () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockProviderWithoutCompany);

      const commandWithUndefinedTaxIds: CalculatePurchaseOrderConditionsCommand = {
        ...manualOrderCommand,
        orderItems: [
          {
            referenceId: 'manual-item-1',
            quantity: 2,
            unitPrice: 120,
            unitPriceAfterDiscount: 110,
            taxIds: undefined,
          },
        ],
      };

      await PurchaseOrderUseCase.calculatePurchaseOrderConditions(commandWithUndefinedTaxIds);

      expect(OrderConditionsDomainService.calculateConditions).toHaveBeenCalledWith(

        expect.objectContaining(
          {
            shippingPrice: 15,
            orderItems: [{
              catalogId: 'manual-item-1',
              quantity: 2,
              discounts: null,
              unitPrice: 120,
              unitPriceAfterDiscount: 110,
              unitPriceAfterDiscountAndTaxes: 110,
              taxes: [],
            }],
          },
        ),
      );

      expect(Registry.TaxRepository.findManyByIds).not.toHaveBeenCalled();
    });

    it('Should return empty taxes for manual items when taxes are not passed', async () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockProviderWithoutCompany);
      (OrderConditionsDomainService.calculateConditions as jest.Mock).mockReturnValueOnce({
        ...mockManualOrderConditions,
        orderItems: [
          {
            ...mockManualOrderConditions.orderItems[0],
            taxes: [],
          },
        ],
      });

      const commandWithoutTaxes: CalculatePurchaseOrderConditionsCommand = {
        ...manualOrderCommand,
        orderItems: [
          {
            referenceId: 'manual-item-1',
            quantity: 2,
            unitPrice: 120,
            unitPriceAfterDiscount: 110,
            taxIds: [],
          },
        ],
      };

      const result = await PurchaseOrderUseCase.calculatePurchaseOrderConditions(commandWithoutTaxes);

      expect(result.orderItems[0].taxes).toEqual([]);
      expect(OrderConditionsDomainService.calculateConditions).toHaveBeenCalledWith(
        expect.objectContaining({
          shippingPrice: 15,
          orderItems: [{
            catalogId: 'manual-item-1',
            quantity: 2,
            discounts: null,
            unitPrice: 120,
            unitPriceAfterDiscount: 110,
            unitPriceAfterDiscountAndTaxes: 110,
            taxes: [],
          }],
        }),

      );

      expect(Registry.TaxRepository.findManyByIds).not.toHaveBeenCalled();
    });

    it('should throw error when client is not a provider client', async () => {
      const clientWithDifferentProvider = {
        ...mockClient,
        companyId: 'different-provider-company-id',
      };

      (Registry.ClientRepository.findManyByClientCompanyIds as jest.Mock).mockResolvedValueOnce([clientWithDifferentProvider]);

      await expect(
        PurchaseOrderUseCase.calculatePurchaseOrderConditions(defaultCommand),
      ).rejects.toThrow('Company company-id is not a client provider');
    });

    it('should throw error when manual item has unitPriceAfterDiscount greater than unitPrice', async () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockProviderWithoutCompany);

      const invalidCommand = {
        ...manualOrderCommand,
        orderItems: [
          {
            referenceId: 'manual-item-1',
            quantity: 2,
            unitPrice: 100,
            unitPriceAfterDiscount: 110, // Price after discount is greater than original price
            taxIds: ['tax-1'],
          },
        ],
      };

      await expect(
        PurchaseOrderUseCase.calculatePurchaseOrderConditions(invalidCommand),
      ).rejects.toThrow('Manual order item should have unitPriceAfterDiscount less than or equal to unitPrice');
    });

    it('should throw error when manual item has no unitPrice', async () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockProviderWithoutCompany);

      const invalidCommand = {
        ...manualOrderCommand,
        orderItems: [
          {
            referenceId: 'manual-item-1',
            quantity: 2,
            unitPriceAfterDiscount: 110,
            taxIds: ['tax-1'],
          },
        ],
      };

      await expect(
        PurchaseOrderUseCase.calculatePurchaseOrderConditions(invalidCommand),
      ).rejects.toThrow('Manual order item should have unitPrice greater than 0');
    });

    it('should throw error when manual item has no unitPriceAfterDiscount', async () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockProviderWithoutCompany);

      const invalidCommand = {
        ...manualOrderCommand,
        orderItems: [
          {
            referenceId: 'manual-item-1',
            quantity: 2,
            unitPrice: 120,
            taxIds: ['tax-1'],
          },
        ],
      };

      await expect(
        PurchaseOrderUseCase.calculatePurchaseOrderConditions(invalidCommand),
      ).rejects.toThrow('Manual order item should have unitPriceAfterDiscount greater than 0');
    });
  });

  describe('obtainInventorySuggestions', () => {
    const mockInventorySuggestionCommand = {
      referenceIds: ['catalog-item-1', 'catalog-item-2'],
      companyId: 'company-id',
      providerId: 'provider-id',
    };

    const mockCatalogItems = [
      {
        id: 'catalog-item-1',
        readId: 'SKU-1',
        companyId: 'provider-company-id',
      },
      {
        id: 'catalog-item-2',
        readId: 'SKU-2',
        companyId: 'provider-company-id',
      },
    ];

    const mockInventoryWithProvider = [
      {
        id: 'inventory-1',
        providers: [
          {
            providerId: 'provider-id',
            providerProductSku: 'SKU-1',
          },
        ],
      },
    ];

    const mockInventoryFromProvider = [
      {
        id: 'inventory-2',
        sku: 'SKU-2',
      },
    ];

    beforeEach(() => {
      jest.clearAllMocks();
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValue(mockProvider);
      (Registry.CatalogRepository.findManyForCompanyByIds as jest.Mock).mockResolvedValue(mockCatalogItems);
      (Registry.InventoryRepository.findManyForCompanyByProviderSkus as jest.Mock)
        .mockImplementation((companyId, providerId, skus) => {
          if (skus.includes('SKU-1') && providerId === 'provider-id') {
            return Promise.resolve(mockInventoryWithProvider);
          }
          return Promise.resolve([]);
        });

      (Registry.InventoryRepository.findManyForCompanyBySkus as jest.Mock)
        .mockImplementation((companyId, skus) => {
          if (skus.includes('SKU-2')) {
            return Promise.resolve(mockInventoryFromProvider);
          }
          return Promise.resolve([]);
        });
    });

    it('should return inventory suggestions for catalog items', async () => {
      const result = await PurchaseOrderUseCase.obtainInventorySuggestions(mockInventorySuggestionCommand);

      expect(Registry.ProviderRepository.findOneById).toHaveBeenCalledWith('provider-id');
      expect(Registry.CatalogRepository.findManyForCompanyByIds).toHaveBeenCalledWith('provider-company-id', ['catalog-item-1', 'catalog-item-2']);

      expect(Registry.InventoryRepository.findManyForCompanyByProviderSkus).toHaveBeenCalledWith(
        'company-id',
        'provider-id',
        ['SKU-1', 'SKU-2'],
      );

      expect(Registry.InventoryRepository.findManyForCompanyBySkus).toHaveBeenCalledWith(
        'company-id',
        ['SKU-2'],
      );

      expect(result).toEqual([
        { referenceId: 'catalog-item-1', inventoryIds: ['inventory-1'] },
        { referenceId: 'catalog-item-2', inventoryIds: ['inventory-2'] },
      ]);
    });

    it('should handle catalog items that have no matching inventory', async () => {
      (Registry.InventoryRepository.findManyForCompanyByProviderSkus as jest.Mock)
        .mockResolvedValueOnce([]); // No already purchased inventory

      (Registry.InventoryRepository.findManyForCompanyBySkus as jest.Mock)
        .mockResolvedValueOnce([]); // No inventory from provider company either

      const result = await PurchaseOrderUseCase.obtainInventorySuggestions(mockInventorySuggestionCommand);

      expect(result).toEqual([
        { referenceId: 'catalog-item-1', inventoryIds: [] },
        { referenceId: 'catalog-item-2', inventoryIds: [] },
      ]);
    });

    it('should throw error when provider not found', async () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(null);

      await expect(
        PurchaseOrderUseCase.obtainInventorySuggestions(mockInventorySuggestionCommand),
      ).rejects.toThrow('Provider not found');
    });

    it('should throw error when provider has no company', async () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockProviderWithoutCompany);

      await expect(
        PurchaseOrderUseCase.obtainInventorySuggestions(mockInventorySuggestionCommand),
      ).rejects.toThrow('Inventory suggestions are only available for order items related to a provider with company');
    });

    it('should throw error when catalog item not found', async () => {
      (Registry.CatalogRepository.findManyForCompanyByIds as jest.Mock).mockResolvedValueOnce([mockCatalogItems[0]]); // Only return one of the items

      await expect(
        PurchaseOrderUseCase.obtainInventorySuggestions(mockInventorySuggestionCommand),
      ).rejects.toThrow('Provider catalog item catalog-item-2 not found');
    });

    it('should handle mixed inventory sources correctly', async () => {
      // First call returns inventory for some items (previously purchased from this provider)
      (Registry.InventoryRepository.findManyForCompanyByProviderSkus as jest.Mock)
        .mockResolvedValueOnce([{
          id: 'inventory-1',
          providers: [{
            providerId: 'provider-id',
            providerProductSku: 'SKU-1',
          }],
        }])
        // Second call returns inventory for remaining items (purchased from provider's company, not this specific provider)
        .mockResolvedValueOnce([]);

      (Registry.InventoryRepository.findManyForCompanyBySkus as jest.Mock)
        .mockResolvedValueOnce([{ id: 'inventory-2', sku: 'SKU-2' }]);

      const result = await PurchaseOrderUseCase.obtainInventorySuggestions(mockInventorySuggestionCommand);

      expect(result).toEqual([
        { referenceId: 'catalog-item-1', inventoryIds: ['inventory-1'] },
        { referenceId: 'catalog-item-2', inventoryIds: ['inventory-2'] },
      ]);
    });
  });

  describe('createPurchaseOrderEntries', () => {
    const mockInventory = {
      id: 'inventory-1',
      companyId: 'company-id',
      name: 'Test Inventory',
    };

    const mockPurchaseOrder: PurchaseOrderEntity = {
      id: 'po-id-1',
      readId: 'PO-001',
      status: PurchaseOrderStatus.PENDING,
      relatedSaleOrderId: 'sale-order-id',
      assignedUserId: 'user-id',
      companyId: 'company-id',
      providerId: 'provider-id',
      shippingPrice: 15,
      shippingAddress: '123 Main St, Anytown, State, Country',
      notes: 'Test notes',
      subtotalBeforeDiscount: 200,
      createdAt: new Date(),
      updatedAt: new Date(),
      subtotal: 180,
      totalDiscount: 20,
      deliveryDate: new Date(),
      shippedAt: null,
      reviewStartedAt: null,
      totalTaxes: 28.8,
      total: 208.8,
      taxes: [{
        name: 'VAT',
        amount: 16,
        type: TaxType.PERCENTAGE,
        value: 14.4,
      }],
      orderItems: [
        {
          id: 'poi-1',
          referenceId: 'catalog-item-1',
          productId: 'SKU-1',
          name: 'Test Item',
          inventoryIds: ['inventory-1'],
          quantity: 2,
          unitPrice: 100,
          createdAt: new Date(),
          updatedAt: new Date(),
          discount: {
            value: 10,
            type: DiscountType.PERCENTAGE,
          },
          subtotal: 180,
          taxes: [{
            name: 'VAT',
            amount: 16,
            type: TaxType.PERCENTAGE,
            value: 14.4,
          }],
          total: 208.8,
          unitPriceAfterDiscount: 90,
          unitPriceAfterDiscountAndTaxes: 104.4,
        }],
    };

    const mockSaleOrder: SaleOrderEntity = {
      ...mockPurchaseOrder,
      id: 'sale-order-id',
      readId: 'SO-001',
      clientId: 'client-id',
      status: SaleOrderStatus.PENDING,
      relatedPurchaseOrderId: 'po-id-1',
      assignedUserId: null,
      orderItems: mockPurchaseOrder.orderItems.map((item) => ({
        ...item,
        catalogId: item.referenceId,
      })),
    };

    // Replace object with string for shipping address
    const mockShippingAddress = '123 Main St, Anytown, State, Country';

    const createCommand = {
      companyId: 'company-id',
      assignedUserId: 'user-id',
      entries: [
        {
          providerId: 'provider-id',
          deliveryDate: new Date(),
          notes: 'Test notes',
          shippingPrice: 15,
          shippingAddress: mockShippingAddress,
          createInventoryFromOrderItems: false,
          orderItems: [
            {
              referenceId: 'catalog-item-1',
              inventoryIds: ['inventory-1'],
              quantity: 2,
              name: 'Test Item',
            },
          ],
        },
      ],
    };

    beforeEach(() => {
      jest.clearAllMocks();
      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockResolvedValue([mockInventory]);

      (Registry.IdentificationService.generateId as jest.Mock).mockResolvedValue('po-id-1');
      (Registry.IdentificationService.generateObjectSequenceForCompany as jest.Mock).mockResolvedValue('PO-001');

      (Registry.PurchaseOrderRepository.save as jest.Mock).mockResolvedValue([mockPurchaseOrder]);
      (Registry.TransactionService.transactional as jest.Mock).mockImplementation((fn) => fn());

      // Replace PurchaseOrderOperator.build mock with OrderDomainService.createPurchaseOrder mock
      (OrderDomainService.createPurchaseOrder as jest.Mock).mockReturnValue({
        purchaseOrder: mockPurchaseOrder,
        inventory: [],
      });

      // Make sure mockCatalogItem has proper inventoryRelations to prevent errors
      const mockCatalogWithInventoryRelations = {
        ...mockCatalogItem,
        inventoryRelations: [],
      };

      (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogWithInventoryRelations]);

      // Mock CatalogMediaRepository
      (Registry.CatalogMediaRepository.findManyByIds as jest.Mock).mockResolvedValue([
        { id: 'media-1', catalogId: 'catalog-item-1', url: 'http://example.com/image.jpg' },
      ]);

      (OrderUseCase.getCatalogConditions as jest.Mock).mockReturnValue([mockOrderItemCalculated]);

      // Add mock for OrderDomainService.createSaleOrder
      (OrderDomainService.createSaleOrder as jest.Mock).mockReturnValue({
        inventory: [],
        saleOrder: mockSaleOrder,
      });
    });

    it('should create a purchase order successfully for catalog items', async () => {
      // Initial purchase order before updating with related sale order ID
      const initialPurchaseOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: undefined,
      };

      // Updated purchase order after adding the related sale order ID
      const updatedPurchaseOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: 'sale-order-id',
      };

      // Configure mocks for related sale order creation
      (Registry.IdentificationService.generateId as jest.Mock)
        .mockResolvedValueOnce('po-id-1') // First call for PO
        .mockResolvedValueOnce('sale-order-id'); // Second call for SO

      (Registry.IdentificationService.generateObjectSequenceForCompany as jest.Mock)
        .mockResolvedValueOnce('PO-001') // First call for PO
        .mockResolvedValueOnce('SO-001'); // Second call for SO's company

      // Mock OrderDomainService.createPurchaseOrder to return the expected values
      (OrderDomainService.createPurchaseOrder as jest.Mock).mockReturnValue({
        purchaseOrder: initialPurchaseOrder,
        inventory: [],
      });

      // Mock the OrderDomainService.createSaleOrder to return the expected values
      (OrderDomainService.createSaleOrder as jest.Mock).mockReturnValue({
        inventory: [],
        saleOrder: mockSaleOrder,
      });

      // First save returns the initial PO, second save returns updated PO with relatedSaleOrderId
      (Registry.PurchaseOrderRepository.save as jest.Mock)
        .mockResolvedValueOnce([initialPurchaseOrder])
        .mockResolvedValueOnce([updatedPurchaseOrder]);

      const result = await PurchaseOrderUseCase.createPurchaseOrderEntries(createCommand);

      expect(Registry.InventoryRepository.findManyByIds).toHaveBeenCalledWith(['inventory-1']);
      expect(Registry.ProviderRepository.findOneById).toHaveBeenCalledWith('provider-id');
      expect(Registry.CatalogRepository.findManyByIds).toHaveBeenCalledWith(['catalog-item-1']);

      // Verify we're calling OrderDomainService.createPurchaseOrder with the right params
      expect(OrderDomainService.createPurchaseOrder).toHaveBeenCalledWith({
        orderBuildParams: expect.objectContaining({
          id: 'po-id-1',
          readId: 'PO-001',
          companyId: 'company-id',
          assignedUserId: 'user-id',
          status: PurchaseOrderStatus.PENDING,
          subtotalBeforeDiscount: 200,
          totalDiscount: 20,
          subtotal: 180,
          totalTaxes: 28.8,
          total: 208.8,
          shippingPrice: 15,
          shippingAddress: mockShippingAddress,
          taxes: [{
            id: 'tax-1', name: 'VAT', amount: 16, type: 'percentage', value: 14.4,
          }],
          orderItems: expect.arrayContaining([
            expect.objectContaining({
              referenceId: 'catalog-item-1',
              name: 'Test Item',
            }),
          ]),
          providerId: 'provider-id',
          deliveryDate: expect.any(Date),
          notes: 'Test notes',
        }),
        createInventoryFromOrderItems: false,
        catalogReference: expect.any(Map),
      });

      expect(Registry.PurchaseOrderRepository.save).toHaveBeenCalledWith([initialPurchaseOrder]);
      expect(Registry.TransactionService.transactional).toHaveBeenCalled();

      expect(result).toEqual([updatedPurchaseOrder]);

      // Verify related sale order creation
      expect(Registry.IdentificationService.generateId).toHaveBeenCalledTimes(2); // Once for PO, once for SO
      expect(Registry.IdentificationService.generateObjectSequenceForCompany).toHaveBeenCalledTimes(2);
      expect(Registry.IdentificationService.generateObjectSequenceForCompany).toHaveBeenNthCalledWith(
        2,
        'provider-company-id',
        'sale_order',
      );

      // Verify OrderDomainService.createSaleOrder was called with the correct parameters
      expect(OrderDomainService.createSaleOrder).toHaveBeenCalledWith(
        expect.objectContaining({
          catalog: expect.any(Array),
          inventory: expect.any(Array),
          orderBuildParams: expect.objectContaining({
            id: 'sale-order-id',
            readId: 'SO-001',
            companyId: 'provider-company-id',
            clientId: 'client-id',
            relatedPurchaseOrderId: 'po-id-1',
            status: SaleOrderStatus.PENDING,
          }),
        }),
      );

      // Verify the SO was saved
      expect(Registry.SaleOrderRepository.save).toHaveBeenCalledWith([mockSaleOrder]);

      // Verify the PO was updated with the SO ID
      expect(Registry.PurchaseOrderRepository.save).toHaveBeenNthCalledWith(
        2,
        [expect.objectContaining({
          relatedSaleOrderId: 'sale-order-id',
        })],
      );
    });

    it('should throw error when inventory is not found', async () => {
      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([]);

      await expect(
        PurchaseOrderUseCase.createPurchaseOrderEntries(createCommand),
      ).rejects.toThrow('Inventory inventory-1 not available');
    });

    it('should throw error when inventory belongs to another company', async () => {
      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([{
        ...mockInventory,
        companyId: 'another-company-id',
      }]);

      await expect(
        PurchaseOrderUseCase.createPurchaseOrderEntries(createCommand),
      ).rejects.toThrow('Inventory inventory-1 forbidden');
    });

    it('should throw error when provider is not found', async () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(null);

      await expect(
        PurchaseOrderUseCase.createPurchaseOrderEntries(createCommand),
      ).rejects.toThrow('Provider provider-id not found');
    });

    it('should validate name for providers without company', async () => {
      const providerWithoutCompany = {
        ...mockProvider,
        providerCompanyId: null,
      };

      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(providerWithoutCompany);
      const commandWithoutName = {
        ...createCommand,
        entries: [{
          ...createCommand.entries[0],
          orderItems: [{
            referenceId: 'manual-item-1',
            inventoryIds: ['inventory-1'],
            quantity: 2,
            // name is missing
          }],
        }],
      };

      await expect(
        PurchaseOrderUseCase.createPurchaseOrderEntries(commandWithoutName),
      ).rejects.toThrow('Order item manual-item-1 name is required for providers without company');
    });

    it('should create multiple purchase orders when multiple entries are provided', async () => {
      const multipleEntriesCommand = {
        ...createCommand,
        entries: [
          createCommand.entries[0],
          {
            providerId: 'provider-id-2',
            deliveryDate: new Date(),
            notes: 'Second order',
            orderItems: [
              {
                referenceId: 'catalog-item-2',
                inventoryIds: ['inventory-2'],
                quantity: 1,
                name: 'Second Item',
              },
            ],
          },
        ],
      };

      const mockPurchaseOrder2 = {
        id: 'po-id-2',
        readId: 'PO-002',
        status: PurchaseOrderStatus.PENDING,
        relatedSaleOrderId: 'sale-order-id',
      } as PurchaseOrderEntity;

      // Setup for second entry with implementation-based mocking
      (Registry.ClientRepository.findManyByClientCompanyIds as jest.Mock).mockResolvedValue([
        mockClient,
        {
          ...mockClient,
          companyId: 'provider-company-id-2',
        },
      ]);
      (Registry.ProviderRepository.findOneById as jest.Mock).mockImplementation((providerId) => {
        if (providerId === 'provider-id') {
          return Promise.resolve(mockProvider);
        }
        if (providerId === 'provider-id-2') {
          return Promise.resolve({
            id: 'provider-id-2',
            providerCompanyId: 'provider-company-id-2',
            name: 'Second Provider',
            companyId: 'company-id',
          });
        }
        return Promise.resolve(null);
      });

      (Registry.CatalogRepository.findManyByIds as jest.Mock).mockImplementation((catalogIds) => {
        const catalogMap: Record<string, Partial<CatalogEntity>> = {
          'catalog-item-1': {
            id: 'catalog-item-1',
            name: 'Test Item',
            readId: 'SKU-1',
            companyId: 'provider-company-id',
            inventoryRelations: [], // Add empty inventory relations
          },
          'catalog-item-2': {
            id: 'catalog-item-2',
            name: 'Second Item',
            readId: 'SKU-2',
            companyId: 'provider-company-id-2',
            inventoryRelations: [], // Add empty inventory relations
          },
        };

        return Promise.resolve(catalogIds.map((id: string) => catalogMap[id]).filter(Boolean));
      });

      (Registry.InventoryRepository.findManyByIds as jest.Mock).mockImplementation((inventoryIds) => {
        const inventoryMap: Record<string, unknown> = {
          'inventory-1': mockInventory,
          'inventory-2': {
            id: 'inventory-2',
            companyId: 'company-id',
            name: 'Second Inventory',
          },
        };

        return Promise.resolve(inventoryIds.map((id: string) => inventoryMap[id]).filter(Boolean));
      });

      (OrderUseCase.getCatalogConditions as jest.Mock).mockImplementation((items) => {
        if (items[0]?.catalogId === 'catalog-item-1') {
          return [mockOrderItemCalculated];
        }
        if (items[0]?.catalogId === 'catalog-item-2') {
          return [{
            catalogId: 'catalog-item-2',
            quantity: 1,
            unitPrice: 100,
            unitPriceAfterDiscount: 90,
            unitPriceAfterDiscountAndTaxes: 104.4,
            discounts: {
              appliedDiscount: {
                catalogDiscount: null,
                storeDiscount: { id: 'discount-2', value: 10 },
              },
              applicableDiscounts: {
                catalogDiscounts: [],
                storeDiscounts: [{ id: 'discount-2', value: 10 }],
              },
            },
            taxes: [{
              id: 'tax-1', name: 'VAT', amount: 16, type: 'percentage', value: 14.4,
            }],
          }];
        }
        return [];
      });

      (OrderConditionsDomainService.calculateConditions as jest.Mock).mockImplementation((item: CalculateConditionsParams) => {
        if (item.orderItems[0].catalogId === 'catalog-item-1') {
          return mockOrderConditions;
        }
        if (item.orderItems[0].catalogId === 'catalog-item-2') {
          return {
            subtotal: 90,
            subtotalBeforeDiscount: 100,
            totalDiscount: 10,
            totalTaxes: 14.4,
            total: 104.4,
            taxes: [{
              id: 'tax-1', name: 'VAT', amount: 16, type: 'percentage', value: 14.4,
            }],
            orderItems: [{
              referenceId: 'catalog-item-2',
              quantity: 1,
              unitPrice: 100,
              unitPriceAfterDiscount: 90,
              unitPriceAfterDiscountAndTaxes: 104.4,
              subtotal: 90,
              total: 104.4,
              taxes: [{
                id: 'tax-1', name: 'VAT', amount: 16, type: 'percentage', value: 14.4,
              }],
            }],
          };
        }
        return null;
      });

      let idCounter = 0;
      // eslint-disable-next-line no-plusplus
      (Registry.IdentificationService.generateId as jest.Mock).mockImplementation(() => Promise.resolve(idCounter++ === 0 ? 'po-id-1' : 'po-id-2'));

      let sequenceCounter = 0;
      (Registry.IdentificationService.generateObjectSequenceForCompany as jest.Mock)
        // eslint-disable-next-line no-plusplus
        .mockImplementation(() => Promise.resolve(sequenceCounter++ === 0 ? 'PO-001' : 'PO-002'));

      (OrderDomainService.createPurchaseOrder as jest.Mock).mockImplementation((params) => {
        if (params.orderBuildParams.id === 'po-id-1') {
          return { purchaseOrder: mockPurchaseOrder, inventory: [] };
        }
        if (params.orderBuildParams.id === 'po-id-2') {
          return { purchaseOrder: mockPurchaseOrder2, inventory: [] };
        }
        return null;
      });

      (OrderDomainService.createSaleOrder as jest.Mock).mockReturnValue({
        inventory: [],
        saleOrder: mockSaleOrder,
      });

      (Registry.PurchaseOrderRepository.save as jest.Mock).mockImplementation((orders) => Promise.resolve(orders));

      const result = await PurchaseOrderUseCase.createPurchaseOrderEntries(multipleEntriesCommand);

      expect(result).toEqual([mockPurchaseOrder, mockPurchaseOrder2]);
      expect(Registry.TransactionService.transactional).toHaveBeenCalledTimes(2);
    });

    it('Should return correctly when no applied discounts', async () => {
      (OrderUseCase.getCatalogConditions as jest.Mock)
        .mockReturnValueOnce([{
          ...mockOrderItemCalculated,
          appliedDiscount: {
            catalogDiscount: null,
            storeDiscount: null,
          },
          applicableDiscounts: {
            catalogDiscounts: [],
            storeDiscounts: [],
          },
        }]);

      const commandWithoutDiscounts: CreatePurchaseOrderEntriesCommand = {
        ...createCommand,
        entries: [
          {
            ...createCommand.entries[0],
            orderItems: [
              {
                referenceId: 'catalog-item-1',
                quantity: 2,
                inventoryIds: [],
              },
            ],
          },
        ],
      };

      const result = await PurchaseOrderUseCase.createPurchaseOrderEntries(commandWithoutDiscounts);

      expect(result).toEqual([mockPurchaseOrder]);
    });

    it('Should use referenceID as productId when provider has no company', async () => {
      const providerWithoutCompany = {
        ...mockProvider,
        providerCompanyId: null,
      };

      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(providerWithoutCompany);

      const commandWithProviderWithoutCompany: CreatePurchaseOrderEntriesCommand = {
        ...createCommand,
        entries: [
          {
            ...createCommand.entries[0],
            orderItems: [
              {
                referenceId: 'catalog-item-1',
                quantity: 2,
                name: 'Test Item',
                unitPrice: 100,
                unitPriceAfterDiscount: 90,
                inventoryIds: ['inventory-1'],
              },
            ],
          },
        ],
      };

      await PurchaseOrderUseCase.createPurchaseOrderEntries(commandWithProviderWithoutCompany);

      expect(OrderDomainService.createPurchaseOrder).toHaveBeenCalledWith({
        orderBuildParams: expect.objectContaining({
          id: 'po-id-1',
          readId: 'PO-001',
          companyId: 'company-id',
          assignedUserId: 'user-id',
          status: PurchaseOrderStatus.PENDING,
          subtotalBeforeDiscount: 200,
          totalDiscount: 20,
          subtotal: 180,
          totalTaxes: 28.8,
          total: 208.8,
          shippingPrice: 15,
          shippingAddress: mockShippingAddress,
          taxes: [{
            id: 'tax-1', name: 'VAT', amount: 16, type: 'percentage', value: 14.4,
          }],
          orderItems: expect.arrayContaining([
            expect.objectContaining({
              referenceId: 'catalog-item-1',
              productId: 'catalog-item-1',
              name: 'Test Item',
              quantity: 2,
              unitPrice: 100,
              unitPriceAfterDiscount: 90,
              unitPriceAfterDiscountAndTaxes: 104.4,
              subtotal: 180,
              total: 208.8,
              taxes: [{
                id: 'tax-1', name: 'VAT', amount: 16, type: 'percentage', value: 14.4,
              }],
            }),
          ]),
          providerId: 'provider-id',
          deliveryDate: expect.any(Date),
          notes: 'Test notes',
        }),
        createInventoryFromOrderItems: false,
        catalogReference: expect.any(Map),
      });
    });

    it('should handle purchase orders without shipping address', async () => {
      const commandWithoutShippingAddress = {
        ...createCommand,
        entries: [
          {
            ...createCommand.entries[0],
            shippingAddress: undefined,
          },
        ],
      };

      await PurchaseOrderUseCase.createPurchaseOrderEntries(commandWithoutShippingAddress);

      // Verify OrderDomainService.createPurchaseOrder was called with undefined shippingAddress
      expect(OrderDomainService.createPurchaseOrder).toHaveBeenCalledWith(
        expect.objectContaining({
          orderBuildParams: expect.objectContaining({
            shippingAddress: undefined,
          }),
        }),
      );
    });

    it('should handle purchase orders without shipping price', async () => {
      const commandWithoutShippingPrice = {
        ...createCommand,
        entries: [
          {
            ...createCommand.entries[0],
            shippingPrice: undefined,
          },
        ],
      };

      await PurchaseOrderUseCase.createPurchaseOrderEntries(commandWithoutShippingPrice);

      // Verify OrderDomainService.createPurchaseOrder was called with undefined shippingPrice
      expect(OrderDomainService.createPurchaseOrder).toHaveBeenCalledWith(
        expect.objectContaining({
          orderBuildParams: expect.objectContaining({
            shippingPrice: undefined,
          }),
        }),
      );

      // Verify we pass undefined shipping price to calculatePurchaseOrderConditions
      expect(OrderConditionsDomainService.calculateConditions).toHaveBeenCalledWith({
        orderItems: expect.any(Array),
        shippingPrice: 0,
      });
    });

    it('should create a related sale order when provider has a company ID', async () => {
      // Initial purchase order before updating with related sale order
      const initialPurchaseOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: undefined,
      };

      // Updated purchase order with related sale order
      const updatedPurchaseOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: 'sale-order-id',
      };

      (Registry.IdentificationService.generateId as jest.Mock)
        .mockResolvedValueOnce('po-id-1') // First call for PO
        .mockResolvedValueOnce('sale-order-id'); // Second call for SO

      (Registry.IdentificationService.generateObjectSequenceForCompany as jest.Mock)
        .mockResolvedValueOnce('PO-001') // First call for PO
        .mockResolvedValueOnce('SO-001'); // Second call for SO's company

      (OrderDomainService.createPurchaseOrder as jest.Mock).mockReturnValue({
        purchaseOrder: initialPurchaseOrder,
        inventory: [],
      });

      // Mock the inventory being updated
      const updatedInventory = [{ id: 'inventory-id-1', sku: 'SKU-1' }];
      (OrderDomainService.createSaleOrder as jest.Mock).mockReturnValue({
        saleOrder: mockSaleOrder,
        inventory: updatedInventory,
      });

      // First save returns the initial PO, second save returns updated PO with relatedSaleOrderId
      (Registry.PurchaseOrderRepository.save as jest.Mock)
        .mockResolvedValueOnce([initialPurchaseOrder])
        .mockResolvedValueOnce([updatedPurchaseOrder]);

      const result = await PurchaseOrderUseCase.createPurchaseOrderEntries(createCommand);

      // Verify the PO was saved initially
      expect(Registry.PurchaseOrderRepository.save).toHaveBeenNthCalledWith(
        1,
        [initialPurchaseOrder],
      );

      // Verify OrderDomainService.createSaleOrder was called
      expect(OrderDomainService.createSaleOrder).toHaveBeenCalled();

      // Verify the SO was saved
      expect(Registry.SaleOrderRepository.save).toHaveBeenCalledWith([mockSaleOrder]);

      // Verify the inventory was saved
      expect(Registry.InventoryRepository.save).toHaveBeenCalledWith(updatedInventory);

      // Verify the PO was updated with just the SO ID
      expect(Registry.PurchaseOrderRepository.save).toHaveBeenNthCalledWith(
        2,
        [expect.objectContaining({
          relatedSaleOrderId: 'sale-order-id',
        })],
      );

      // Verify the final result has the updated PO with the related sale order ID
      expect(result).toEqual([updatedPurchaseOrder]);
    });

    it('should handle inventory updates correctly when creating a sale order', async () => {
      // Initial purchase order
      const initialPurchaseOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: undefined,
      };

      // Updated purchase order with related sale order
      const updatedPurchaseOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: 'sale-order-id',
      };

      // Mock catalog items with inventory relations
      const catalogWithInventoryRelations = [
        {
          ...mockCatalogItem,
          inventoryRelations: [
            { inventoryId: 'inventory-1' },
            { inventoryId: 'inventory-2' },
          ],
        },
      ];

      (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue(catalogWithInventoryRelations);

      // Mock updated inventory
      const updatedInventory = [
        { id: 'inventory-1', quantity: 5 },
        { id: 'inventory-2', quantity: 10 },
      ];

      // Mock the related inventory
      (Registry.InventoryRepository.findManyByIds as jest.Mock)
        .mockResolvedValueOnce([mockInventory]) // For the initial inventory check
        .mockResolvedValueOnce(updatedInventory); // For the sale order inventory

      (OrderDomainService.createSaleOrder as jest.Mock).mockReturnValue({
        saleOrder: mockSaleOrder,
        inventory: updatedInventory,
      });

      (OrderDomainService.createPurchaseOrder as jest.Mock).mockReturnValue({
        purchaseOrder: initialPurchaseOrder,
        inventory: [],
      });

      (Registry.PurchaseOrderRepository.save as jest.Mock)
        .mockResolvedValueOnce([initialPurchaseOrder])
        .mockResolvedValueOnce([updatedPurchaseOrder]);

      await PurchaseOrderUseCase.createPurchaseOrderEntries(createCommand);

      // Verify inventory was fetched for related inventory items
      expect(Registry.InventoryRepository.findManyByIds).toHaveBeenCalledWith(['inventory-1', 'inventory-2']);

      // Verify the updated inventory was saved
      expect(Registry.InventoryRepository.save).toHaveBeenCalledWith(updatedInventory);
    });

    it('should not save inventory if none is returned from createSaleOrder', async () => {
      // Initial purchase order
      const initialPurchaseOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: undefined,
      };

      // Updated purchase order with related sale order
      const updatedPurchaseOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: 'sale-order-id',
      };

      // Setup mocks
      (OrderDomainService.createPurchaseOrder as jest.Mock).mockReturnValue({
        purchaseOrder: initialPurchaseOrder,
        inventory: [],
      });

      // Make sure the mock for createSaleOrder returns an empty array specifically
      (OrderDomainService.createSaleOrder as jest.Mock).mockReturnValue({
        saleOrder: mockSaleOrder,
        inventory: [],
      });

      // Reset the save mock to track calls accurately
      (Registry.InventoryRepository.save as jest.Mock).mockReset();

      (Registry.PurchaseOrderRepository.save as jest.Mock)
        .mockResolvedValueOnce([initialPurchaseOrder])
        .mockResolvedValueOnce([updatedPurchaseOrder]);

      await PurchaseOrderUseCase.createPurchaseOrderEntries(createCommand);

      // Verify inventory save was not called since we specifically don't want it called with empty arrays
      expect(Registry.InventoryRepository.save).not.toHaveBeenCalled();
    });

    it('should not create a related sale order when provider has no company ID', async () => {
      const providerWithoutCompany = {
        ...mockProvider,
        providerCompanyId: null,
      };

      (Registry.ProviderRepository.findOneById as jest.Mock)
        .mockResolvedValueOnce(providerWithoutCompany);

      // Mock the purchase order without related sale order ID
      const purchaseOrderWithoutRelatedSO = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: undefined,
      };

      (OrderDomainService.createPurchaseOrder as jest.Mock).mockReturnValue({
        purchaseOrder: purchaseOrderWithoutRelatedSO,
        inventory: [],
      });

      (Registry.PurchaseOrderRepository.save as jest.Mock).mockResolvedValue([purchaseOrderWithoutRelatedSO]);

      const commandWithoutSaleOrder: CreatePurchaseOrderEntriesCommand = {
        ...createCommand,
        entries: [
          {
            ...createCommand.entries[0],
            orderItems: [
              {
                referenceId: 'catalog-item-1',
                inventoryIds: ['inventory-1'],
                quantity: 2,
                name: 'Test Item',
                unitPrice: 100,
                unitPriceAfterDiscount: 90,
              },
            ],
          },
        ],
      };

      const result = await PurchaseOrderUseCase.createPurchaseOrderEntries(commandWithoutSaleOrder);

      // Verify sale order was not created
      expect(Registry.SaleOrderRepository.save).not.toHaveBeenCalled();
      expect(SaleOrderOperator.build).not.toHaveBeenCalled();

      // Verify purchase order repository was called only once
      expect(Registry.PurchaseOrderRepository.save).toHaveBeenCalledTimes(1);

      // Verify the result doesn't have a related sale order ID
      expect(result[0].relatedSaleOrderId).toBeUndefined();
    });

    it('should create inventory items when createInventoryFromOrderItems is true', async () => {
      const mockInventoryItems = [
        { id: 'new-inventory-1', name: 'New Inventory Item', sku: 'SKU-1' },
      ];

      // Setup command with createInventoryFromOrderItems=true
      const commandWithCreateInventory = {
        ...createCommand,
        entries: [
          {
            ...createCommand.entries[0],
            createInventoryFromOrderItems: true,
          },
        ],
      };

      // Mock catalog with attributes
      const mockCatalogWithAttributes = {
        ...mockCatalogItem,
        attributes: [{ name: 'color', value: 'red' }],
      };

      // Mock finding no existing inventory with the same SKU
      (Registry.InventoryRepository.findManyForCompanyBySkus as jest.Mock).mockResolvedValue([]);
      (Registry.CatalogRepository.findManyByIds as jest.Mock).mockResolvedValue([mockCatalogWithAttributes]);

      // Mock OrderDomainService.createPurchaseOrder to return inventory items
      (OrderDomainService.createPurchaseOrder as jest.Mock).mockReturnValue({
        purchaseOrder: mockPurchaseOrder,
        inventory: mockInventoryItems,
      });

      await PurchaseOrderUseCase.createPurchaseOrderEntries(commandWithCreateInventory);

      // Verify OrderDomainService.createPurchaseOrder was called with createInventoryFromOrderItems=true
      expect(OrderDomainService.createPurchaseOrder).toHaveBeenCalledWith({
        orderBuildParams: expect.any(Object),
        createInventoryFromOrderItems: true,
        catalogReference: expect.any(Map),
      });

      // Verify inventory SKUs are checked for existence
      expect(Registry.InventoryRepository.findManyForCompanyBySkus).toHaveBeenCalledWith(
        'company-id',
        ['SKU-1'],
      );
    });

    it('should not save inventory when no items are returned from createPurchaseOrder', async () => {
      // Mock OrderDomainService.createPurchaseOrder to return empty inventory array
      (OrderDomainService.createPurchaseOrder as jest.Mock).mockReturnValue({
        purchaseOrder: mockPurchaseOrder,
        inventory: [],
      });

      await PurchaseOrderUseCase.createPurchaseOrderEntries(createCommand);

      // InventoryRepository.save should not be called with empty array
      expect(Registry.InventoryRepository.save).not.toHaveBeenCalledWith([]);
      expect(Registry.InventoryRepository.findManyForCompanyBySkus).not.toHaveBeenCalled();
    });

    it('should throw error when inventory already exists', async () => {
      const mockInventoryItems = [
        { id: 'new-inventory-1', name: 'New Inventory Item', sku: 'SKU-1' },
        { id: 'new-inventory-2', name: 'New Inventory Item 2', sku: 'SKU-2' },
      ];

      const existingInventory = [
        { id: 'existing-inventory-1', name: 'Existing Inventory', sku: 'SKU-1' },
      ];

      const commandWithCreateInventory = {
        ...createCommand,
        entries: [
          {
            ...createCommand.entries[0],
            createInventoryFromOrderItems: true,
          },
        ],
      };

      // Mock OrderDomainService.createPurchaseOrder to return inventory items
      (OrderDomainService.createPurchaseOrder as jest.Mock).mockReturnValue({
        purchaseOrder: mockPurchaseOrder,
        inventory: mockInventoryItems,
      });

      // Mock finding existing inventory with the same SKU
      (Registry.InventoryRepository.findManyForCompanyBySkus as jest.Mock).mockResolvedValue(existingInventory);

      await expect(
        PurchaseOrderUseCase.createPurchaseOrderEntries(commandWithCreateInventory),
      ).rejects.toThrow('Inventory SKU-1 already exists');
    });

    it('should create inventory without attributes when provider has no company', async () => {
      const providerWithoutCompany = {
        ...mockProvider,
        providerCompanyId: null,
      };

      const mockInventoryItems = [
        { id: 'new-inventory-1', name: 'New Inventory Item', sku: 'SKU-1' },
      ];

      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(providerWithoutCompany);
      (Registry.InventoryRepository.findManyForCompanyBySkus as jest.Mock).mockResolvedValue([]);

      const commandWithCreateInventory = {
        ...createCommand,
        entries: [
          {
            ...createCommand.entries[0],
            createInventoryFromOrderItems: true,
            orderItems: [
              {
                referenceId: 'catalog-item-1',
                quantity: 2,
                name: 'Manual Item',
                unitPrice: 100,
                unitPriceAfterDiscount: 90,
                inventoryIds: [],
              },
            ],
          },
        ],
      };

      // Mock OrderDomainService.createPurchaseOrder to return inventory items
      (OrderDomainService.createPurchaseOrder as jest.Mock).mockReturnValue({
        purchaseOrder: mockPurchaseOrder,
        inventory: mockInventoryItems,
      });

      await PurchaseOrderUseCase.createPurchaseOrderEntries(commandWithCreateInventory);

      // Verify inventory items are saved without attributes modification
      expect(Registry.InventoryRepository.save).toHaveBeenCalledWith(mockInventoryItems);
    });

    it('should correctly pass catalog media to OrderDomainService.createPurchaseOrder', async () => {
      // Initial purchase order before updating with related sale order ID
      const initialPurchaseOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: undefined,
      };

      // Updated purchase order after adding the related sale order ID
      const updatedPurchaseOrder = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: 'sale-order-id',
      };

      // Configure mocks
      (Registry.IdentificationService.generateId as jest.Mock)
        .mockResolvedValueOnce('po-id-1')
        .mockResolvedValueOnce('sale-order-id');

      (Registry.IdentificationService.generateObjectSequenceForCompany as jest.Mock)
        .mockResolvedValueOnce('PO-001')
        .mockResolvedValueOnce('SO-001');

      (OrderDomainService.createPurchaseOrder as jest.Mock).mockReturnValue({
        purchaseOrder: initialPurchaseOrder,
        inventory: [],
      });

      (Registry.PurchaseOrderRepository.save as jest.Mock)
        .mockResolvedValueOnce([initialPurchaseOrder])
        .mockResolvedValueOnce([updatedPurchaseOrder]);

      await PurchaseOrderUseCase.createPurchaseOrderEntries(createCommand);

      // Verify that the catalogReference is correctly passed to OrderDomainService.createPurchaseOrder
      expect(OrderDomainService.createPurchaseOrder).toHaveBeenCalledWith(
        expect.objectContaining({
          orderBuildParams: expect.any(Object),
          createInventoryFromOrderItems: false,
          catalogReference: expect.any(Map),
        }),
      );

      // Verify CatalogMediaRepository was called
      expect(Registry.CatalogMediaRepository.findManyByIds).toHaveBeenCalled();
    });
  });

  describe('updatePurchaseOrderEntry', () => {
    const mockPurchaseOrder: PurchaseOrderEntity = {
      id: 'po-id-1',
      readId: 'PO-001',
      status: PurchaseOrderStatus.PENDING,
      relatedSaleOrderId: 'sale-order-id',
      assignedUserId: 'user-id',
      companyId: 'company-id',
      providerId: 'provider-id',
      shippingPrice: 15,
      shippingAddress: '123 Main St, Anytown, State, Country',
      notes: 'Test notes',
      subtotalBeforeDiscount: 200,
      createdAt: new Date(),
      updatedAt: new Date(),
      subtotal: 180,
      totalDiscount: 20,
      deliveryDate: new Date('2023-01-01'),
      shippedAt: null,
      reviewStartedAt: null,
      totalTaxes: 28.8,
      total: 208.8,
      taxes: [{
        name: 'VAT',
        amount: 16,
        type: TaxType.PERCENTAGE,
        value: 14.4,
      }],
      orderItems: [
        {
          id: 'poi-1',
          referenceId: 'catalog-item-1',
          productId: 'SKU-1',
          name: 'Test Item',
          inventoryIds: ['inventory-1'],
          quantity: 2,
          unitPrice: 100,
          createdAt: new Date(),
          updatedAt: new Date(),
          discount: {
            value: 10,
            type: DiscountType.PERCENTAGE,
          },
          subtotal: 180,
          taxes: [{
            name: 'VAT',
            amount: 16,
            type: TaxType.PERCENTAGE,
            value: 14.4,
          }],
          total: 208.8,
          unitPriceAfterDiscount: 90,
          unitPriceAfterDiscountAndTaxes: 104.4,
        }],
    };

    const mockSaleOrder: SaleOrderEntity = {
      ...mockPurchaseOrder,
      id: 'sale-order-id',
      readId: 'SO-001',
      clientId: 'client-id',
      status: SaleOrderStatus.PENDING,
      relatedPurchaseOrderId: 'po-id-1',
      assignedUserId: null,
      orderItems: mockPurchaseOrder.orderItems.map((item) => ({
        ...item,
        catalogId: item.referenceId,
      })),
    };

    const mockUpdatedPurchaseOrder = {
      ...mockPurchaseOrder,
      status: PurchaseOrderStatus.PROCESSING,
      notes: 'Updated notes',
      deliveryDate: new Date('2023-02-01'),
      shippingAddress: 'New shipping address',
    };

    const mockUpdatedSaleOrder = {
      ...mockSaleOrder,
      status: SaleOrderStatus.PROCESSING,
      shippingAddress: 'New shipping address',
    };

    const updateCommand = {
      companyId: 'company-id',
      purchaseOrder: {
        id: 'po-id-1',
        status: PurchaseOrderStatus.PROCESSING,
        notes: 'Updated notes',
        deliveryDate: new Date('2023-02-01'),
        shippingAddress: 'New shipping address',
        assignedUserId: 'new-user-id',
      },
    };

    beforeEach(() => {
      jest.clearAllMocks();
      (Registry.PurchaseOrderRepository.findOneById as jest.Mock).mockResolvedValue(mockPurchaseOrder);
      (Registry.SaleOrderRepository.findOneById as jest.Mock).mockResolvedValue(mockSaleOrder);
      (OrderUseCase.validateOrderPatch as jest.Mock).mockResolvedValue(true);
      (PurchaseOrderOperator.update as jest.Mock).mockReturnValue(mockUpdatedPurchaseOrder);
      (SaleOrderOperator.update as jest.Mock).mockReturnValue(mockUpdatedSaleOrder);
      (Registry.PurchaseOrderRepository.save as jest.Mock).mockResolvedValue([mockUpdatedPurchaseOrder]);
      (Registry.SaleOrderRepository.save as jest.Mock).mockResolvedValue([mockUpdatedSaleOrder]);
      (Registry.TransactionService.transactional as jest.Mock).mockImplementation((fn) => fn());
      // Add mock for OrderDomainService.updateSaleOrderStatus
      (OrderDomainService.updateSaleOrderStatus as jest.Mock).mockReturnValue({
        saleOrder: mockUpdatedSaleOrder,
      });
      // Add mock for OrderDomainService.updatePurchaseOrderStatus
      (OrderDomainService.updatePurchaseOrderStatus as jest.Mock).mockReturnValue({
        purchaseOrder: mockUpdatedPurchaseOrder,
        inventory: [],
      });
    });

    it('should update a purchase order successfully with status change and inventory updates', async () => {
      const mockUpdatedInventory = [
        { id: 'inventory-1', quantity: 8 },
        { id: 'inventory-2', quantity: 4 },
      ];

      (OrderDomainService.updatePurchaseOrderStatus as jest.Mock).mockReturnValueOnce({
        purchaseOrder: mockUpdatedPurchaseOrder,
        inventory: mockUpdatedInventory,
      });

      const result = await PurchaseOrderUseCase.updatePurchaseOrderEntry(updateCommand);

      // Verify PurchaseOrderOperator.update was called correctly without status
      expect(PurchaseOrderOperator.update).toHaveBeenCalledWith(
        mockPurchaseOrder,
        {
          deliveryDate: updateCommand.purchaseOrder.deliveryDate,
          shippingAddress: 'New shipping address',
          notes: 'Updated notes',
          assignedUserId: 'new-user-id',
        },
        'company-id',
      );

      // Verify OrderDomainService.updatePurchaseOrderStatus was called
      expect(OrderDomainService.updatePurchaseOrderStatus).toHaveBeenCalledWith({
        order: mockUpdatedPurchaseOrder,
        status: PurchaseOrderStatus.PROCESSING,
        modifierCompanyId: 'company-id',
        inventory: expect.any(Array),
      });

      // Verify inventory was saved
      expect(Registry.InventoryRepository.save).toHaveBeenCalledWith(mockUpdatedInventory);

      expect(result).toEqual(mockUpdatedPurchaseOrder);
    });

    it('should update a purchase order successfully', async () => {
      const result = await PurchaseOrderUseCase.updatePurchaseOrderEntry(updateCommand);

      expect(Registry.PurchaseOrderRepository.findOneById).toHaveBeenCalledWith('po-id-1');
      expect(OrderUseCase.validateOrderPatch).toHaveBeenCalledWith({
        companyId: 'company-id',
        entity: mockPurchaseOrder,
        params: {
          id: 'po-id-1',
          status: PurchaseOrderStatus.PROCESSING,
          notes: 'Updated notes',
          deliveryDate: updateCommand.purchaseOrder.deliveryDate,
          shippingAddress: 'New shipping address',
          assignedUserId: 'new-user-id',
        },
      });

      expect(PurchaseOrderOperator.update).toHaveBeenCalledWith(
        mockPurchaseOrder,
        {
          notes: 'Updated notes',
          deliveryDate: updateCommand.purchaseOrder.deliveryDate,
          shippingAddress: 'New shipping address',
          assignedUserId: 'new-user-id',
        },
        'company-id',
      );

      expect(Registry.PurchaseOrderRepository.save).toHaveBeenCalledWith([mockUpdatedPurchaseOrder]);
      expect(Registry.TransactionService.transactional).toHaveBeenCalled();

      expect(result).toEqual(mockUpdatedPurchaseOrder);
    });

    it('should update related sale order when it exists', async () => {
      const result = await PurchaseOrderUseCase.updatePurchaseOrderEntry(updateCommand);

      // Verify sale order was fetched and updated
      expect(Registry.SaleOrderRepository.findOneById).toHaveBeenCalledWith('sale-order-id');
      expect(SaleOrderOperator.update).toHaveBeenCalledWith(
        mockSaleOrder,
        {
          shippingAddress: 'New shipping address',
        },
      );

      // Verify OrderDomainService was called to update status
      expect(OrderDomainService.updateSaleOrderStatus).toHaveBeenCalledWith({
        order: mockUpdatedSaleOrder,
        status: PurchaseOrderStatus.PROCESSING, // The actual status from command, not hardcoded
        modifierCompanyId: 'company-id',
      });

      expect(Registry.SaleOrderRepository.save).toHaveBeenCalledWith([mockUpdatedSaleOrder]);

      expect(result).toEqual(mockUpdatedPurchaseOrder);
    });

    it('should not update related sale order when it does not exist', async () => {
      // Set up mock purchase order without related sale order ID
      const purchaseOrderWithoutRelation = {
        ...mockPurchaseOrder,
        relatedSaleOrderId: null,
      };

      (Registry.PurchaseOrderRepository.findOneById as jest.Mock).mockResolvedValue(purchaseOrderWithoutRelation);

      await PurchaseOrderUseCase.updatePurchaseOrderEntry(updateCommand);

      // Verify sale order repository was not accessed
      expect(Registry.SaleOrderRepository.findOneById).not.toHaveBeenCalled();
      expect(SaleOrderOperator.update).not.toHaveBeenCalled();
      expect(Registry.SaleOrderRepository.save).not.toHaveBeenCalled();
    });

    it('should throw error when related sale order not found', async () => {
      (Registry.SaleOrderRepository.findOneById as jest.Mock).mockResolvedValueOnce(null);

      await expect(
        PurchaseOrderUseCase.updatePurchaseOrderEntry(updateCommand),
      ).rejects.toThrow('Related sale order not found');
    });

    it('should throw error when order validation fails', async () => {
      (OrderUseCase.validateOrderPatch as jest.Mock).mockRejectedValue(
        new Error('Invalid status transition'),
      );

      await expect(
        PurchaseOrderUseCase.updatePurchaseOrderEntry(updateCommand),
      ).rejects.toThrow('Invalid status transition');
    });

    it('should use existing assignedUserId when not provided in the update', async () => {
      const commandWithoutAssignedUser = {
        companyId: 'company-id',
        purchaseOrder: {
          id: 'po-id-1',
          status: PurchaseOrderStatus.PROCESSING,
          notes: 'Updated notes',
          deliveryDate: new Date('2023-02-01'),
          shippingAddress: 'New shipping address',
        },
      };

      await PurchaseOrderUseCase.updatePurchaseOrderEntry(commandWithoutAssignedUser);

      // Verify that update was called with the existing assignedUserId
      expect(PurchaseOrderOperator.update).toHaveBeenCalledWith(
        mockPurchaseOrder,
        {
          notes: 'Updated notes',
          deliveryDate: commandWithoutAssignedUser.purchaseOrder.deliveryDate,
          shippingAddress: 'New shipping address',
          assignedUserId: 'user-id', // Should use the existing value
        },
        'company-id',
      );
    });

    it('should not update the sale order status when no status is provided', async () => {
      const commandWithoutStatus = {
        companyId: 'company-id',
        purchaseOrder: {
          id: 'po-id-1',
          notes: 'Updated notes',
          deliveryDate: new Date('2023-02-01'),
          shippingAddress: 'New shipping address',
          assignedUserId: 'new-user-id',
        },
      };

      await PurchaseOrderUseCase.updatePurchaseOrderEntry(commandWithoutStatus);

      // Verify sale order update was called only with shippingAddress
      expect(SaleOrderOperator.update).toHaveBeenCalledWith(
        mockSaleOrder,
        {
          shippingAddress: 'New shipping address',
        },
      );

      // Verify OrderDomainService was not called
      expect(OrderDomainService.updateSaleOrderStatus).not.toHaveBeenCalled();
    });

    it('should not update inventory when no items have inventory IDs', async () => {
      const purchaseOrderWithoutInventory = {
        ...mockPurchaseOrder,
        orderItems: [
          {
            ...mockPurchaseOrder.orderItems[0],
            inventoryIds: [],
          },
        ],
      };

      (Registry.PurchaseOrderRepository.findOneById as jest.Mock).mockResolvedValue(purchaseOrderWithoutInventory);

      await PurchaseOrderUseCase.updatePurchaseOrderEntry(updateCommand);

      // Verify empty inventory array is passed
      expect(OrderDomainService.updatePurchaseOrderStatus).toHaveBeenCalledWith(expect.objectContaining({
        inventory: [],
      }));
      expect(Registry.InventoryRepository.save).not.toHaveBeenCalled();
    });

    it('should not process inventory when status is not provided', async () => {
      const commandWithoutStatus = {
        companyId: 'company-id',
        purchaseOrder: {
          id: 'po-id-1',
          notes: 'Updated notes',
          deliveryDate: new Date('2023-02-01'),
          shippingAddress: 'New shipping address',
          assignedUserId: 'new-user-id',
        },
      };

      await PurchaseOrderUseCase.updatePurchaseOrderEntry(commandWithoutStatus);

      // Verify service methods were not called
      expect(OrderDomainService.updatePurchaseOrderStatus).not.toHaveBeenCalled();
      expect(Registry.InventoryRepository.findManyByIds).not.toHaveBeenCalled();
      expect(Registry.InventoryRepository.save).not.toHaveBeenCalled();
    });
  });

  describe('getAvailableStatuses', () => {
    const mockPurchaseOrder: PurchaseOrderEntity = {
      id: 'po-id-1',
      readId: 'PO-001',
      status: PurchaseOrderStatus.PENDING,
      companyId: 'company-id',
      providerId: 'provider-id',
      assignedUserId: 'user-id',
      shippingPrice: 15,
      subtotalBeforeDiscount: 200,
      subtotal: 180,
      totalDiscount: 20,
      totalTaxes: 28.8,
      total: 208.8,
      createdAt: new Date(),
      updatedAt: new Date(),
      taxes: [],
      orderItems: [],
      deliveryDate: null,
      notes: null,
      relatedSaleOrderId: null,
      reviewStartedAt: null,
      shippedAt: null,
      shippingAddress: null,
    };

    const mockAvailableStatuses = [
      PurchaseOrderStatus.PENDING,
      PurchaseOrderStatus.PROCESSING,
    ];

    beforeEach(() => {
      jest.clearAllMocks();
      (Registry.PurchaseOrderRepository.findOneById as jest.Mock).mockResolvedValue(mockPurchaseOrder);
      (PurchaseOrderOperator.availableStatuses as jest.Mock).mockReturnValue(mockAvailableStatuses);
    });

    it('should return available statuses for a purchase order', async () => {
      const command = {
        companyId: 'company-id',
        purchaseOrderId: 'po-id-1',
      };

      const result = await PurchaseOrderUseCase.getAvailableStatuses(command);

      expect(Registry.PurchaseOrderRepository.findOneById).toHaveBeenCalledWith('po-id-1');
      expect(PurchaseOrderOperator.availableStatuses).toHaveBeenCalledWith(mockPurchaseOrder);
      expect(result).toEqual(mockAvailableStatuses);
    });

    it('should throw error when purchase order not found', async () => {
      (Registry.PurchaseOrderRepository.findOneById as jest.Mock).mockResolvedValueOnce(null);

      const command = {
        companyId: 'company-id',
        purchaseOrderId: 'invalid-id',
      };

      await expect(
        PurchaseOrderUseCase.getAvailableStatuses(command),
      ).rejects.toThrow('Purchase order not found');
    });

    it('should throw error when purchase order does not belong to company', async () => {
      const command = {
        companyId: 'different-company-id',
        purchaseOrderId: 'po-id-1',
      };

      await expect(
        PurchaseOrderUseCase.getAvailableStatuses(command),
      ).rejects.toThrow('Purchase order does not belong to company');
    });
  });
});
