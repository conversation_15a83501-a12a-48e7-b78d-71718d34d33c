import CreateProviderEntriesCommand from '#application/provider/commands/CreateProviderEntries.Command';
import UpdateProviderEntryCommand from '#application/provider/commands/UpdateProviderEntry.Command';
import ProviderUseCase from '#application/provider/useCases/Provider.UseCase';
import Registry from '#composition/ImplementationRegistry';
import ProviderEntity from '#domain/aggregates/provider/Provider.Entity';
import ProviderOperator from '#domain/aggregates/provider/Provider.Operator';

jest.unmock('./Provider.UseCase');
jest.unmock('lodash');

describe('ProviderUseCase', () => {
  const mockDate = new Date('2023-01-01');

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    jest.setSystemTime(mockDate);
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('createProviderEntries', () => {
    const command: CreateProviderEntriesCommand = {
      entries: [{
        name: 'China Parts On Fire',
        tributaryId: '*********',
        providerCompanyId: 'prov_123',
      }],
      companyId: 'prov_124',
    };

    const provider: ProviderEntity = {
      id: 'prov123',
      name: command.entries[0].name,
      tributaryId: '*********',
      providerCompanyId: 'prov_123',
      companyId: command.companyId,
    };

    it('Should throw an error when repeated names are provided', async () => {
      const commandWithRepeatedNames: CreateProviderEntriesCommand = {
        entries: [
          {
            name: 'China Parts On Fire',
            tributaryId: '*********',
            providerCompanyId: 'prov_123',
          },
          {
            name: 'China Parts On Fire',
            tributaryId: '*********',
            providerCompanyId: 'prov_123',
          },
        ],
        companyId: 'prov_124',
      };

      expect(ProviderUseCase.createProviderEntries(commandWithRepeatedNames)).rejects.toThrow('Name: "China Parts On Fire" is provided for more than one entry');
    });

    it('Should throw an error when provided names are already taken', async () => {
      (Registry.ProviderRepository.findManyByNamesAndCompanyId as jest.Mock).mockResolvedValueOnce([{ name: 'China Parts On Fire' }]);

      expect(ProviderUseCase.createProviderEntries(command)).rejects.toThrow('Some names were alredy taken: China Parts On Fire');
    });

    it('Should create new providers', async () => {
      (Registry.IdentificationService.generateId as jest.Mock).mockResolvedValueOnce('prov123');
      (Registry.ProviderRepository.saveMany as jest.Mock).mockResolvedValueOnce(provider);
      (Registry.CompanyRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([{ id: 'prov_123' }]);
      (ProviderOperator.build as jest.Mock).mockReturnValueOnce(provider);

      const gotProvider = await ProviderUseCase.createProviderEntries(command);

      expect(gotProvider).toEqual(provider);
      expect(Registry.IdentificationService.generateId).toHaveBeenCalled();
      expect(ProviderOperator.build).toHaveBeenCalledWith({ ...command.entries[0], id: 'prov123', companyId: command.companyId });
      expect(Registry.ProviderRepository.saveMany).toHaveBeenCalledWith([provider]);
    });

    it('Should create new providers without providerCompanyIds', async () => {
      const testCommand: CreateProviderEntriesCommand = {
        entries: [{
          name: 'China Parts On Fire',
          tributaryId: '*********',
        }],
        companyId: 'prov_124',
      };

      (Registry.IdentificationService.generateId as jest.Mock).mockResolvedValueOnce('prov123');
      (Registry.ProviderRepository.saveMany as jest.Mock).mockResolvedValueOnce(provider);
      (ProviderOperator.build as jest.Mock).mockReturnValueOnce(provider);

      const gotProvider = await ProviderUseCase.createProviderEntries(testCommand);

      expect(gotProvider).toEqual(provider);
      expect(Registry.IdentificationService.generateId).toHaveBeenCalled();
      expect(ProviderOperator.build).toHaveBeenCalledWith({ ...testCommand.entries[0], id: 'prov123', companyId: command.companyId });
      expect(Registry.ProviderRepository.saveMany).toHaveBeenCalledWith([provider]);
    });

    it('Should throw an error when providerCompanyIds are not found', async () => {
      (Registry.CompanyRepository.findManyByIds as jest.Mock).mockResolvedValueOnce([]);

      expect(ProviderUseCase.createProviderEntries(command)).rejects.toThrow(`Some company were not found: ${command.entries[0].providerCompanyId}`);
    });
  });

  describe('deleteProviderEntry', () => {
    const mockProvider = {
      id: 'cat123',
      companyId: 'comp123',
    };

    it('should delete a provider entry', async () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockProvider);

      await ProviderUseCase.deleteProviderEntry(mockProvider.id, mockProvider.companyId);

      expect(Registry.ProviderRepository.findOneById).toHaveBeenCalledWith(mockProvider.id);
      expect(Registry.ProviderRepository.deleteById).toHaveBeenCalledWith(mockProvider.id);
    });

    it('should throw an error when the provider was not found', async () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(undefined);

      expect(ProviderUseCase.deleteProviderEntry(mockProvider.id, mockProvider.companyId)).rejects.toThrow(new Error('Provider not found'));

      expect(Registry.ProviderRepository.findOneById).toHaveBeenCalledWith(mockProvider.id);
      expect(Registry.ProviderRepository.deleteById).not.toHaveBeenCalled();
    });

    it('should throw an error when the provider does not belong to the provided company', async () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(mockProvider);

      expect(ProviderUseCase.deleteProviderEntry(mockProvider.id, 'wrongcompany')).rejects.toThrow(new Error('Provider does not belong to the provided company'));

      expect(Registry.ProviderRepository.findOneById).toHaveBeenCalledWith(mockProvider.id);
      expect(Registry.ProviderRepository.deleteById).not.toHaveBeenCalled();
    });
  });

  describe('updateProviderEntry', () => {
    const command: UpdateProviderEntryCommand = {
      id: 'prov_123',
      name: 'China Parts On Fire',
      tributaryId: '*********',
      companyId: 'comp_123',
    };

    it('should return an error if the provider is not found', () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(undefined);

      expect(ProviderUseCase.updateProviderEntry(command)).rejects.toThrow('Provider not found');
    });

    it('should return an error if the provider does not belong to the company', () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce({ companyId: 'other_company' });

      expect(ProviderUseCase.updateProviderEntry(command)).rejects.toThrow('Forbidden');
    });

    it('should return an error if the provider does not belong to the company', () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce({ companyId: command.companyId, providerCompanyId: 'comp_123' });

      expect(ProviderUseCase.updateProviderEntry(command)).rejects.toThrow('Provider can\'t be updated');
    });

    it('should return an error if the updated name already exists', () => {
      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce({ companyId: command.companyId, name: 'previous_name' });
      (Registry.ProviderRepository.findOneByNameAndCompanyId as jest.Mock).mockResolvedValueOnce({ name: command.name });

      expect(ProviderUseCase.updateProviderEntry(command)).rejects.toThrow('Provider name already taken');
    });

    it('should update a provider correctly', async () => {
      const foundProvider: ProviderEntity = {
        companyId: command.companyId,
        name: 'previous_name',
        id: command.id,
        tributaryId: null,
        providerCompanyId: null,
      };

      (Registry.ProviderRepository.findOneById as jest.Mock).mockResolvedValueOnce(foundProvider);
      (Registry.ProviderRepository.findOneByNameAndCompanyId as jest.Mock).mockResolvedValueOnce(undefined);
      (ProviderOperator.update as jest.Mock).mockReturnValueOnce(command);
      (Registry.ProviderRepository.save as jest.Mock).mockResolvedValueOnce(command);

      const updated = await ProviderUseCase.updateProviderEntry(command);

      expect(Registry.ProviderRepository.findOneById).toHaveBeenCalledWith(command.id);
      expect(Registry.ProviderRepository.findOneByNameAndCompanyId).toHaveBeenCalledWith(command.name, command.companyId);
      expect(ProviderOperator.update).toHaveBeenCalledWith(foundProvider, command);
      expect(updated).toEqual(command);
    });
  });
});
