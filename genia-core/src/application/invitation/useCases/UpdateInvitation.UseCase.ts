import { ApplicationError } from '#application/Common.Type';
import CompanyErrorCodes from '#application/company/Company.ErrorCodes';
import UpdateInvitationCommand from '#application/invitation/commands/UpdateInvitation.Command';
import InvitationErrorCodes from '#application/invitation/Invitation.ErrorCodes';
import { Invitation, InvitationState, InvitationType } from '#application/invitation/services/Invitation.Service';
import { Notification, NotificationType } from '#application/notification/services/Notification.Service';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import ClientOperator from '#domain/aggregates/client/Client.Operator';
import ProviderOperator from '#domain/aggregates/provider/Provider.Operator';

async function handleStateTransition(invitation: Invitation): Promise<void> {
  const { state, id: invitationId } = invitation;

  const providerCompanyId = invitation.type === InvitationType.PROVIDER ? invitation.invitedCompanyId! : invitation.companyId;
  const clientCompanyId = invitation.type === InvitationType.CLIENT ? invitation.invitedCompanyId! : invitation.companyId;

  if (state === InvitationState.ACCEPTED) {
    const providerCompany = await Registry.CompanyRepository.findOneById(providerCompanyId);
    if (!providerCompany) {
      throw new ApplicationError(`Provider company with id ${providerCompanyId} not found`, Cause.NOT_FOUND, CompanyErrorCodes.COMPANY_NOT_FOUND);
    }

    const clientCompany = await Registry.CompanyRepository.findOneById(clientCompanyId);
    if (!clientCompany) {
      throw new ApplicationError(`Client company with id ${clientCompanyId} not found`, Cause.NOT_FOUND, CompanyErrorCodes.COMPANY_NOT_FOUND);
    }

    const providerEntity = ProviderOperator.build({
      id: await Registry.IdentificationService.generateId(),
      name: providerCompany.name,
      tributaryId: providerCompany.tributaryId,
      providerCompanyId: providerCompany.id,
      companyId: clientCompany.id,
    });

    await Registry.ProviderRepository.save(providerEntity);

    const clientEntity = ClientOperator.build({
      id: await Registry.IdentificationService.generateId(),
      name: clientCompany.name,
      tributaryId: clientCompany.tributaryId,
      clientCompanyId: clientCompany.id,
      companyId: providerCompany.id,
    });

    await Registry.ClientRepository.save(clientEntity);

    await Registry.NotificationService.deleteByInvitationId(invitationId);
  }
}

async function apply(command: UpdateInvitationCommand): Promise<Invitation> {
  const { invitationId, userId, ...updates } = command;

  const { invitedCompanyId: newCompanyId, state: newState } = updates;

  const invitation = await Registry.InvitationService.findOneById(invitationId);
  if (!invitation) {
    throw new ApplicationError(`Invitation with id ${invitationId} not found`, Cause.NOT_FOUND, InvitationErrorCodes.INVITATION_NOT_FOUND);
  }

  const { invitedCompanyId: oldCompanyId, state: oldState } = invitation;

  if (oldCompanyId && newCompanyId && (oldCompanyId !== newCompanyId)) {
    throw new ApplicationError('Invited company should not be updated', Cause.FORBIDDEN, InvitationErrorCodes.INVITATION_FORBIDDEN);
  }

  const updatedInvitation: Invitation = { ...invitation, ...updates };

  return Registry.TransactionService.transactional(async () => {
    if (newState && (oldState !== newState)) {
      await handleStateTransition(updatedInvitation);
    }

    const [result] = await Registry.InvitationService.save([updatedInvitation]);

    if (newCompanyId && !oldCompanyId) {
      const notification: Notification = {
        id: await Registry.IdentificationService.generateId(),
        ownerUserId: userId,
        payload: {
          invitationId,
        },
        companyId: newCompanyId,
        type: invitation.type === InvitationType.CLIENT ? NotificationType.CLIENT_INVITATION : NotificationType.PROVIDER_INVITATION,
        requiredRoles: [],
        createdAt: new Date(Date.now()),
      };

      await Registry.NotificationService.save([notification]);
    }

    return result;
  });
}

export default {
  apply,
};
