import { RetrieveUserCommand } from '#application/user/commands/RetrieveUser.Command';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import Logger from '#composition/Logger';
import CompanyEntity from '#domain/aggregates/company/Company.Entity';
import CompanyOperator from '#domain/aggregates/company/Company.Operator';
import UserEntity, { UserRole } from '#domain/aggregates/user/User.Entity';
import UserOperator from '#domain/aggregates/user/User.Operator';

import RegisterCommand from '../commands/Register.Command';

const logger = Logger.getLogger();

async function register(registerCommand: RegisterCommand): Promise<{ user: UserEntity, company: CompanyEntity }> {
  const {
    user: {
      email, lastName, name, phoneNumber,
    },
    company: {
      name: companyName, description, tributaryId, country,
    },
  } = registerCommand;

  const userByEmail = await Registry.UserRepository.findOneByEmail(email);

  if (userByEmail) {
    throw new Error('Email already exists', { cause: Cause.CONFLICT });
  }

  const userByPhone = await Registry.UserRepository.findOneByPhoneNumber(phoneNumber);

  if (userByPhone) {
    throw new Error('Phone number already exists', { cause: Cause.CONFLICT });
  }

  const existingCompany = await Registry.CompanyRepository.findOneByTributaryIdAndCountry(tributaryId, country);

  if (existingCompany) {
    throw new Error(`Company with ${tributaryId} already exists`, { cause: Cause.CONFLICT });
  }

  return Registry.TransactionService.transactional(async () => {
    const company = await Registry.CompanyRepository.save(CompanyOperator.build({
      id: await Registry.IdentificationService.generateId(),
      name: companyName,
      description,
      tributaryId,
      country,
    }));

    const processingUser = await Registry.UserRepository.createOne(UserOperator.build({
      id: await Registry.IdentificationService.generateId(),
      email,
      companies: [company.id],
      lastName,
      name,
      role: UserRole.ADMIN,
      phoneNumber,
    }));

    return { user: processingUser, company };
  }).catch(() => {
    logger.error('Error registering user', { registerCommand });

    throw new Error('Error registering user');
  });
}

async function retrieveUser(command: RetrieveUserCommand): Promise<UserEntity> {
  const { userId } = command;

  const user = await Registry.UserRepository.findOneById(userId);

  if (!user) {
    throw new Error(`User with ID ${userId} not found`, { cause: Cause.NOT_FOUND });
  }

  return user;
}

const UserUseCase = {
  register,
  retrieveUser,
};

export default UserUseCase;
