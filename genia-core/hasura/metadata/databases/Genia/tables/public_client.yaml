table:
  name: client
  schema: public
configuration:
  column_config:
    client_company_id:
      custom_name: clientCompanyId
    company_id:
      custom_name: companyId
    created_at:
      custom_name: createdAt
    tributary_id:
      custom_name: tributaryId
    updated_at:
      custom_name: updatedAt
  custom_column_names:
    client_company_id: clientCompanyId
    company_id: companyId
    created_at: createdAt
    tributary_id: tributaryId
    updated_at: updatedAt
  custom_root_fields: {}
object_relationships:
  - name: client_company
    using:
      foreign_key_constraint_on: client_company_id
  - name: company
    using:
      foreign_key_constraint_on: company_id
array_relationships:
  - name: catalog_discounts
    using:
      foreign_key_constraint_on:
        column: client_id
        table:
          name: catalog_discount_client
          schema: public
select_permissions:
  - role: read:hasura
    permission:
      columns:
        - company_id
        - id
        - name
        - tributary_id
      filter: {}
    comment: ""
  - role: read_user
    permission:
      columns:
        - name
        - tributary_id
        - created_at
        - updated_at
        - client_company_id
        - company_id
        - id
      filter:
        company_id:
          _eq: X-Hasura-Company-Id
      allow_aggregations: true
    comment: ""
