import { createContext, ReactNode, useContext, useState } from 'react';

// Interfaces para los datos del cliente
interface ClientFormData {
  profilePhoto?: string;
  clientType: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  contactPerson?: string;
  clientAddress: string;
  email: string;
  phone: string;
  isWhatsApp: boolean;
  whatsappNumber: string;
  location: string;
  // Contactos alternativos
  cobranzaEmail: string;
  cobranzaPhone: string;
  cobranzaWhatsApp: string;
  comprasEmail: string;
  comprasPhone: string;
  comprasWhatsApp: string;
  ventasEmail: string;
  ventasPhone: string;
  ventasWhatsApp: string;
  propertyType: string;
  budget: string;
  preferredContact: string;
  tags: string[];
  notes: string;
}

interface ClientPayload {
  profilePhoto?: string;
  clientType: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  contactPerson?: string;
  clientAddress: string;
  email: string;
  phone: string;
  isWhatsApp: boolean;
  whatsappNumber: string;
  location: string;
  alternativeContacts: {
    cobranza: {
      email: string;
      phone: string;
      whatsapp: string;
    };
    compras: {
      email: string;
      phone: string;
      whatsapp: string;
    };
    ventas: {
      email: string;
      phone: string;
      whatsapp: string;
    };
  };
  propertyType: string;
  budget: string;
  preferredContact: string;
  tags: string[];
  notes: string;
}

interface ClientFormContextType {
  formData: ClientFormData;
  updateField: (field: keyof ClientFormData, value: any) => void;
  addTag: (tag: string) => void;
  removeTag: (tag: string) => void;
  getPayload: () => ClientPayload;
  resetForm: () => void;
}

const initialFormData: ClientFormData = {
  profilePhoto: undefined,
  clientType: 'Persona',
  firstName: '',
  lastName: '',
  companyName: '',
  contactPerson: '',
  clientAddress: '',
  email: '',
  phone: '',
  isWhatsApp: false,
  whatsappNumber: '',
  location: '',
  cobranzaEmail: '',
  cobranzaPhone: '',
  cobranzaWhatsApp: '',
  comprasEmail: '',
  comprasPhone: '',
  comprasWhatsApp: '',
  ventasEmail: '',
  ventasPhone: '',
  ventasWhatsApp: '',
  propertyType: '',
  budget: '',
  preferredContact: 'WhatsApp',
  tags: [],
  notes: '',
};

const ClientFormContext = createContext<ClientFormContextType | undefined>(undefined);

export function ClientFormProvider({ children }: { children: ReactNode }) {
  const [formData, setFormData] = useState<ClientFormData>(initialFormData);

  const updateField = (field: keyof ClientFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };



  const addTag = (tag: string) => {
    if (tag.trim() && !formData.tags.includes(tag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag.trim()],
      }));
    }
  };

  const removeTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag),
    }));
  };

  const getPayload = (): ClientPayload => {
    return {
      profilePhoto: formData.profilePhoto,
      clientType: formData.clientType,
      firstName: formData.firstName,
      lastName: formData.lastName,
      companyName: formData.companyName,
      contactPerson: formData.contactPerson,
      clientAddress: formData.clientAddress,
      email: formData.email,
      phone: formData.phone,
      isWhatsApp: formData.isWhatsApp,
      whatsappNumber: formData.whatsappNumber,
      location: formData.location,
      alternativeContacts: {
        cobranza: {
          email: formData.cobranzaEmail,
          phone: formData.cobranzaPhone,
          whatsapp: formData.cobranzaWhatsApp,
        },
        compras: {
          email: formData.comprasEmail,
          phone: formData.comprasPhone,
          whatsapp: formData.comprasWhatsApp,
        },
        ventas: {
          email: formData.ventasEmail,
          phone: formData.ventasPhone,
          whatsapp: formData.ventasWhatsApp,
        },
      },
      propertyType: formData.propertyType,
      budget: formData.budget,
      preferredContact: formData.preferredContact,
      tags: formData.tags,
      notes: formData.notes,
    };
  };

  const resetForm = () => {
    setFormData(initialFormData);
  };

  return (
    <ClientFormContext.Provider value={{
      formData,
      updateField,
      addTag,
      removeTag,
      getPayload,
      resetForm,
    }}>
      {children}
    </ClientFormContext.Provider>
  );
}

export function useClientForm() {
  const context = useContext(ClientFormContext);
  if (context === undefined) {
    throw new Error('useClientForm must be used within a ClientFormProvider');
  }
  return context;
}
