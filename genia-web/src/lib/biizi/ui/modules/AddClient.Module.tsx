import { Button } from '@pitsdepot/storybook';
import { useRef } from 'react';

import { AddClientForm, AddClientFormRef } from '#/lib/biizi/ui/components/AddClientForm.Component';

export function AddClientModule() {
  const formRef = useRef<AddClientFormRef>(null);

  const handleSaveClick = () => {
    if (formRef.current) {
      formRef.current.handleSave();
    }
  };

  return (
    <div className="min-h-screen bg-transparent p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between w-full mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Nuevo Cliente</h1>
            <p className="text-sm text-gray-600">Crea un nuevo cliente en el sistema</p>
          </div>
          <Button
            variant="primary"
            size="small"
            onClick={handleSaveClick}
          >
            Guardar cambios
          </Button>
        </div>
        {/* <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Link to="/clients">
              <IconImporter
                name="caretLeft"
                size={24}
                className="text-gray-600 hover:text-gray-800 cursor-pointer transition-colors"
              />
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Nuevo Cliente</h1>
              <p className="text-gray-600">Crea un nuevo cliente en el sistema</p>
            </div>
          </div>
        </div> */}

        {/* Formulario */}

        <AddClientForm ref={formRef} />

        {/* <ClientForm
          client={newClient}
          onSave={handleSave}
          onCancel={handleCancel}
          isLoading={isLoading}
        /> */}
      </div>
    </div>
  );
}
