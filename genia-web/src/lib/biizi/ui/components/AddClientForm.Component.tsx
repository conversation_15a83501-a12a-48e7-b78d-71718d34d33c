import type { OptionsDropdownProps } from '@pitsdepot/storybook';
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardHeader,
  Card<PERSON><PERSON>le,
  DropdownSimple,
  FormInputType,
  IconImporter,
  InputComponent,
  Label,
  TextAreaInput,
} from '@pitsdepot/storybook';
import { motion } from 'framer-motion';
import { useState } from 'react';

interface ClientFormData {
  profilePhoto?: string;
  clientType: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  contactPerson?: string;
  clientAddress: string;
  email: string;
  phone: string;
  isWhatsApp: boolean;
  whatsappNumber: string;
  location: string;
  // Contactos alternativos
  cobranzaEmail: string;
  cobranzaPhone: string;
  cobranzaWhatsApp: string;
  comprasEmail: string;
  comprasPhone: string;
  comprasWhatsApp: string;
  ventasEmail: string;
  ventasPhone: string;
  ventasWhatsApp: string;
  propertyType: string;
  budget: string;
  preferredContact: string;
  tags: string[];
  notes: string;
}

export function AddClientForm() {
  const [formData, setFormData] = useState<ClientFormData>({
    profilePhoto: undefined,
    clientType: 'Persona',
    firstName: '',
    lastName: '',
    companyName: '',
    contactPerson: '',
    clientAddress: '',
    email: '',
    phone: '',
    isWhatsApp: false,
    whatsappNumber: '',
    location: '',
    // Contactos alternativos
    cobranzaEmail: '',
    cobranzaPhone: '',
    cobranzaWhatsApp: '',
    comprasEmail: '',
    comprasPhone: '',
    comprasWhatsApp: '',
    ventasEmail: '',
    ventasPhone: '',
    ventasWhatsApp: '',
    propertyType: '',
    budget: '',
    preferredContact: 'WhatsApp',
    tags: [],
    notes: '',
  });

  const [errors, setErrors] = useState({
    firstName: '',
    lastName: '',
    email: '',
    cobranzaEmail: '',
    comprasEmail: '',
    ventasEmail: '',
  });

  const [newTag, setNewTag] = useState('');

  const clientTypeOptions = [
    { id: 'Persona', name: 'Persona' },
    { id: 'Empresa', name: 'Empresa' },
  ];

  const preferredContactOptions = [
    { id: 'WhatsApp', name: 'WhatsApp' },
    { id: 'Email', name: 'Email' },
    { id: 'Teléfono', name: 'Teléfono' },
  ];

  const propertyTypes = [
    'Departamento',
    'Casa',
    'Terreno',
    'Local Comercial',
    'Oficina',
  ];

  const handleBack = () => {
    // Handle navigation back
  };

  const handleSave = () => {
    // Preparar el payload con todos los campos del formulario
    const payload = {
      // Información principal
      profilePhoto: formData.profilePhoto,
      clientType: formData.clientType,
      firstName: formData.firstName,
      lastName: formData.lastName,
      companyName: formData.companyName,
      contactPerson: formData.contactPerson,
      clientAddress: formData.clientAddress,
      email: formData.email,
      phone: formData.phone,
      isWhatsApp: formData.isWhatsApp,
      whatsappNumber: formData.whatsappNumber,
      location: formData.location,

      // Contactos alternativos
      alternativeContacts: {
        cobranza: {
          email: formData.cobranzaEmail,
          phone: formData.cobranzaPhone,
          whatsapp: formData.cobranzaWhatsApp,
        },
        compras: {
          email: formData.comprasEmail,
          phone: formData.comprasPhone,
          whatsapp: formData.comprasWhatsApp,
        },
        ventas: {
          email: formData.ventasEmail,
          phone: formData.ventasPhone,
          whatsapp: formData.ventasWhatsApp,
        },
      },

      // Preferencias (comentadas pero incluidas para cuando se activen)
      propertyType: formData.propertyType,
      budget: formData.budget,
      preferredContact: formData.preferredContact,
      tags: formData.tags,

      // Notas
      notes: formData.notes,
    };

    console.log('=== PAYLOAD PARA GUARDAR CLIENTE ===');
    console.log(JSON.stringify(payload, null, 2));
    console.log('=== FIN DEL PAYLOAD ===');

    // TODO: Implementar llamada al API cuando esté listo
    // await saveClient(payload);
  };

  const handleCancel = () => {
    // Handle cancel action
  };

  const handlePhotoChange = () => {
    // Handle photo change
  };

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()],
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((tag) => tag !== tagToRemove),
    }));
  };

  const validateName = (name: string): boolean => {
    // Only allow letters and spaces
    const nameRegex = /^[a-zA-ZÀ-ÿ\s]*$/;
    return nameRegex.test(name);
  };

  const validateEmail = (email: string): boolean => {
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhoneNumber = (phone: string): boolean => {
    // Only allow + and numbers
    const phoneRegex = /^[+0-9]*$/;
    return phoneRegex.test(phone);
  };

  const handleInputChange = (field: keyof ClientFormData, value: string | boolean) => {
    // Validate firstName and lastName to only accept letters and spaces
    if (field === 'firstName' || field === 'lastName') {
      const stringValue = String(value);
      if (!validateName(stringValue)) {
        // Don't update the field if it contains invalid characters
        setErrors((prev) => ({
          ...prev,
          [field]: 'Este campo solo acepta letras y espacios',
        }));
        return;
      }
      // Clear error if input is valid
      setErrors((prev) => ({
        ...prev,
        [field]: '',
      }));
    }

    // Validate email field
    if (field === 'email' || field === 'cobranzaEmail' || field === 'comprasEmail' || field === 'ventasEmail') {
      const stringValue = String(value);
      if (stringValue && !validateEmail(stringValue)) {
        setErrors((prev) => ({
          ...prev,
          [field]: 'Por favor ingrese un email válido',
        }));
      } else {
        // Clear error if input is valid or empty
        setErrors((prev) => ({
          ...prev,
          [field]: '',
        }));
      }
    }

    // Validate phone and WhatsApp fields
    if (field === 'phone' || field === 'whatsappNumber' || field === 'cobranzaPhone'
        || field === 'cobranzaWhatsApp' || field === 'comprasPhone' || field === 'comprasWhatsApp'
        || field === 'ventasPhone' || field === 'ventasWhatsApp') {
      const stringValue = String(value);
      if (stringValue && !validatePhoneNumber(stringValue)) {
        // Don't update the field if it contains invalid characters
        setErrors((prev) => ({
          ...prev,
          [field]: 'Este campo solo acepta + y números',
        }));
        return;
      }
      // Clear error if input is valid or empty
      setErrors((prev) => ({
        ...prev,
        [field]: '',
      }));
    }

    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.4 }}
      className="max-w-4xl mx-auto space-y-6"
    >
      {/* Header */}

      {/* Información Principal */}
      <Card className='bg-white'>
        <CardHeader >
          <CardTitle className="text-lg font-semibold text-gray-900">
            Información Principal
          </CardTitle>
          <p className="text-sm text-gray-600">Datos principales del cliente</p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Profile Photo */}
          {/* <div className="flex items-center gap-4">
            <Avatar
              size={80}
              name={formData.fullName || 'Foto de perfil'}
              src={formData.profilePhoto}
            />
            <Button
              variant="outlined"
              size="small"
              onClick={handlePhotoChange}
              className="text-sm"
            >
              Cambiar foto
            </Button>
          </div> */}

          <div className="grid grid-cols-1 gap-6">
            {/* Client Type and Name Fields - Side by side */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Client Type */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  Tipo de cliente
                </Label>
                <DropdownSimple
                  options={clientTypeOptions}
                  setSelectedOption={(option: OptionsDropdownProps) => handleInputChange('clientType', option.id)}
                  showAvatar={false}
                >
                  <div
                    className="w-full px-3 py-[6px] border border-gray-300 rounded-md
                    focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer
                    flex items-center justify-between"
                  >
                    <span>{clientTypeOptions.find((type) => type.id === formData.clientType)?.name}</span>
                    <IconImporter name="caretDown" />
                  </div>
                </DropdownSimple>
              </div>

              {/* Name Fields - To the right of client type */}
              {formData.clientType === 'Empresa' ? (
                // Contact Person for Empresa
                <div className="space-y-2 md:col-span-2">
                  <Label className="text-sm font-medium text-gray-700">
                    Nombre de encargado
                  </Label>
                  <InputComponent
                    name="contactPerson"
                    placeholder="Nombre del encargado"
                    value={formData.contactPerson || ''}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('contactPerson', e.target.value)}
                  />
                </div>
              ) : (
                // First Name and Last Name for Persona
                <div className="space-y-2 md:col-span-2">
                  <Label className="text-sm font-medium text-gray-700">
                    Nombre completo
                  </Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <InputComponent
                        name="firstName"
                        placeholder="Nombre"
                        value={formData.firstName}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('firstName', e.target.value)}
                      />
                      {errors.firstName && (
                        <p className="text-sm text-red-500">{errors.firstName}</p>
                      )}
                    </div>
                    <div className="space-y-1">
                      <InputComponent
                        name="lastName"
                        placeholder="Apellido"
                        value={formData.lastName}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('lastName', e.target.value)}
                      />
                      {errors.lastName && (
                        <p className="text-sm text-red-500">{errors.lastName}</p>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Company Name - Below client type and name fields, only shown for Empresa */}
            {formData.clientType === 'Empresa' && (
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  Nombre de la empresa
                </Label>
                <InputComponent
                  name="companyName"
                  placeholder="Nombre de la empresa"
                  value={formData.companyName || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('companyName', e.target.value)}
                />
              </div>
            )}
          </div>

          {/* Client Address */}
          <div className="space-y-2 md:col-span-2">
            <Label className="text-sm font-medium text-gray-700">
              Dirección del cliente
            </Label>
            <InputComponent
              name="clientAddress"
              placeholder="Dirección completa"
              value={formData.clientAddress}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('clientAddress', e.target.value)}
            />
          </div>

          {/* Email and Phone - Side by side */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:col-span-2">
            {/* Email */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Email principal
              </Label>
              <InputComponent
                name="email"
                inputType={'email' as FormInputType}
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('email', e.target.value)}
              />
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email}</p>
              )}
            </div>

            {/* Phone */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Teléfono / WhatsApp principal
              </Label>
              <InputComponent
                name="phone"
                placeholder="+52 ************"
                value={formData.phone}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('phone', e.target.value)}
              />
              {/* WhatsApp Checkbox - Below phone field */}
              <div className="flex items-center space-x-2 pt-2">
                <input
                  type="checkbox"
                  id="isWhatsApp"
                  checked={formData.isWhatsApp}
                  onChange={(e) => handleInputChange('isWhatsApp', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <Label
                  htmlFor="isWhatsApp"
                  className="text-sm font-medium text-gray-700"
                >
                  ¿Este número es también WhatsApp?
                </Label>
              </div>
            </div>
          </div>

          {/* WhatsApp Number */}
          {!formData.isWhatsApp && (
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">
              Número de WhatsApp
            </Label>
            <InputComponent
              name="whatsappNumber"
              placeholder="+52 ************"
              value={formData.whatsappNumber}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('whatsappNumber', e.target.value)}
                />
          </div>
          )}

          {/* Location */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">
              Ubicación
            </Label>
            <InputComponent
              name="location"
              placeholder="Ciudad, Estado"
              value={formData.location}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('location', e.target.value)}
              />
          </div>
        </CardContent>
      </Card>

      {/* Contactos alternativos por categoría */}
      <Card className='bg-white'>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">
            Información de contacto por area de negocio
          </CardTitle>
          <p className="text-sm text-gray-600">
            Puedes agregar email, teléfono y WhatsApp alternativos para Cobranza, Compras y Ventas.
            Si algún contacto alternativo no está definido, se usará el contacto principal del cliente.
          </p>
        </CardHeader>
        <CardContent className="space-y-8">
          {/* Cobranza */}
          <div className="space-y-4">
            <h3 className="text-base font-semibold text-gray-900">Cobranza</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Email</Label>
                <InputComponent
                  name="cobranzaEmail"
                  placeholder="<EMAIL>"
                  value={formData.cobranzaEmail}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('cobranzaEmail', e.target.value)}
                />
                {errors.cobranzaEmail && (
                  <p className="text-sm text-red-500">{errors.cobranzaEmail}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Teléfono</Label>
                <InputComponent
                  name="cobranzaPhone"
                  placeholder="+52 ************"
                  value={formData.cobranzaPhone}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('cobranzaPhone', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">WhatsApp</Label>
                <InputComponent
                  name="cobranzaWhatsApp"
                  placeholder="+52 ************"
                  value={formData.cobranzaWhatsApp}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('cobranzaWhatsApp', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Compras */}
          <div className="space-y-4">
            <h3 className="text-base font-semibold text-gray-900">Compras</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Email</Label>
                <InputComponent
                  name="comprasEmail"
                  placeholder="<EMAIL>"
                  value={formData.comprasEmail}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('comprasEmail', e.target.value)}
                />
                {errors.comprasEmail && (
                  <p className="text-sm text-red-500">{errors.comprasEmail}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Teléfono</Label>
                <InputComponent
                  name="comprasPhone"
                  placeholder="+52 ************"
                  value={formData.comprasPhone}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('comprasPhone', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">WhatsApp</Label>
                <InputComponent
                  name="comprasWhatsApp"
                  placeholder="+52 ************"
                  value={formData.comprasWhatsApp}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('comprasWhatsApp', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Ventas */}
          <div className="space-y-4">
            <h3 className="text-base font-semibold text-gray-900">Ventas</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Email</Label>
                <InputComponent
                  name="ventasEmail"
                  placeholder="<EMAIL>"
                  value={formData.ventasEmail}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('ventasEmail', e.target.value)}
                />
                {errors.ventasEmail && (
                  <p className="text-sm text-red-500">{errors.ventasEmail}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Teléfono</Label>
                <InputComponent
                  name="ventasPhone"
                  placeholder="+52 ************"
                  value={formData.ventasPhone}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('ventasPhone', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">WhatsApp</Label>
                <InputComponent
                  name="ventasWhatsApp"
                  placeholder="+52 ************"
                  value={formData.ventasWhatsApp}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('ventasWhatsApp', e.target.value)}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Preferencias */}
      {/* <Card className='bg-white'>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">
            Preferencias
          </CardTitle>
          <p className="text-sm text-gray-600">Preferencias de búsqueda y presupuesto</p>
        </CardHeader>
        <CardContent className="space-y-">
          <div className="space-y-3">
            <Label className="text-sm font-medium text-gray-700">
              Tipo de propiedad
            </Label>
            <div className="flex flex-wrap gap-2">
              {propertyTypes.map((type) => (
                <Button
                  key={type}
                  variant={formData.propertyType === type ? 'primary' : 'outlined'}
                  size="small"
                  onClick={() => handleInputChange('propertyType', type)}
                  className="text-sm"
                >
                  {type}
                </Button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Presupuesto
              </Label>
              <InputComponent
                name="budget"
                placeholder="Ej: $2,000,000 - $3,500,000 MXN"
                value={formData.budget}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange('budget', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Canal de contacto preferido
              </Label>
              <DropdownSimple
                options={preferredContactOptions}
                setSelectedOption={(option: OptionsDropdownProps) => handleInputChange('preferredContact', option.id)}
                showAvatar={false}
              >
                <div
                  className="w-full px-3 py-[6px] border border-gray-300 rounded-md
                  focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer
                  flex items-center justify-between"
                >
                  <span>{preferredContactOptions.find((type) => type.id === formData.preferredContact)?.name}</span>
                  <IconImporter name="caretDown" />
                </div>
              </DropdownSimple>
            </div>
          </div>
        </CardContent>
      </Card> */}

      {/* Etiquetas */}
      {/* <Card className='bg-white'>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">
            Etiquetas
          </CardTitle>
          <p className="text-sm text-gray-600">Etiquetas para categorizar al cliente</p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <InputComponent
              name="newTag"
              placeholder="Nueva etiqueta"
              value={newTag}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewTag(e.target.value)}
              onKeyPress={(e: React.KeyboardEvent) => e.key === 'Enter' && handleAddTag()}
              className="flex-1"
            />
            <Button
              variant="primary"
              size="small"
              onClick={handleAddTag}
              disabled={!newTag.trim()}
            >
              Agregar
            </Button>
          </div>

          {formData.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {formData.tags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {tag}
                  <button
                    type="button"
                    onClick={() => handleRemoveTag(tag)}
                    className="ml-1 hover:text-blue-600"
                  >
                    <IconImporter name="x" size={12} />
                  </button>
                </span>
              ))}
            </div>
          )}
        </CardContent>
      </Card> */}

      {/* Notas */}
      <Card className='bg-white'>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">
            Notas
          </CardTitle>
          <p className="text-sm text-gray-600">Información adicional sobre el cliente</p>
        </CardHeader>
        <CardContent>
          <TextAreaInput
            name="notes"
            placeholder="Escribe notas adicionales sobre el cliente..."
            value={formData.notes}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleInputChange('notes', e.target.value)}
            rows={4}
            className="w-full"
          />
        </CardContent>
      </Card>

    </motion.div>
  );
}
